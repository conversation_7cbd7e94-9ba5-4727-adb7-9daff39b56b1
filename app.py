import asyncio
import base64
import json
from pathlib import Path
import os
import hashlib
import requests
import urllib.request
import urllib.error
import ssl
import aiohttp  # pip install aiohttp
import gradio as gr
import numpy as np
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.responses import HTMLResponse, StreamingResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastrtc import (
    AdditionalOutputs,
    AsyncStreamHandler,
    Stream,
    get_twilio_turn_credentials,
    wait_for_item,
)
import uuid
from gradio.utils import get_space
import struct
import string
import sys
from datetime import datetime
from typing import List, Dict, Optional, Any

# 添加thirdparty路径以便导入模块
sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "tb_speech_search"))

from tts8 import ByteDanceTTSStreaming, StreamConfig, TriggerStrategy
from chat import InterruptibleAzureOpenAI
from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool
from search_agent import fetch_rag

class InterruptEvent:
    pass

class ChatEvent:
    pass

class SearchResultEvent:
    """搜索结果事件"""
    pass

class VolumeControlEvent:
    """音量控制事件"""
    pass

class ConversationTurn:
    """对话轮次数据结构"""
    def __init__(self, turn_id: str, user_input: str, intent: str, confidence: float,
                 timestamp: str = None, agent_response: str = "", search_query: str = "",
                 rewritten_query: str = ""):
        self.turn_id = turn_id
        self.user_input = user_input
        self.intent = intent
        self.confidence = confidence
        self.timestamp = timestamp or datetime.now().isoformat()
        self.agent_response = agent_response
        self.search_query = search_query  # 原始搜索查询
        self.rewritten_query = rewritten_query  # 改写后的查询

class ConversationMemory:
    """对话记忆管理类"""
    def __init__(self, max_turns: int = 10):
        self.session_id = str(uuid.uuid4())
        self.conversation_history: List[ConversationTurn] = []
        self.max_turns = max_turns
        self.current_search_context = {}  # 当前搜索上下文
        self.user_preferences = {}  # 用户偏好
        self.last_search_intent_turn = None  # 最后一次搜索意图的轮次

    def add_turn(self, turn: ConversationTurn):
        """添加对话轮次"""
        self.conversation_history.append(turn)

        # 如果是搜索意图，更新搜索上下文
        if turn.intent == 'search':
            self.last_search_intent_turn = turn
            self._update_search_context(turn)

        # 保持历史记录在限制范围内
        if len(self.conversation_history) > self.max_turns:
            self.conversation_history.pop(0)

    def _update_search_context(self, turn: ConversationTurn):
        """更新搜索上下文"""
        # 提取搜索相关信息
        user_input = turn.user_input.lower()

        # 提取价格信息
        import re
        price_patterns = [
            r'(\d+)元以下', r'(\d+)元以内', r'不超过(\d+)元', r'低于(\d+)元',
            r'(\d+)块以下', r'(\d+)块以内', r'不超过(\d+)块', r'低于(\d+)块',
            r'(\d+)-(\d+)元', r'(\d+)到(\d+)元'
        ]

        for pattern in price_patterns:
            match = re.search(pattern, user_input)
            if match:
                if '到' in pattern or '-' in pattern:
                    self.current_search_context['price_range'] = {
                        'min': int(match.group(1)),
                        'max': int(match.group(2))
                    }
                else:
                    self.current_search_context['max_price'] = int(match.group(1))
                break

        # 提取价格相关的情感表达
        price_sentiment_patterns = [
            '我的钱不够', '我的錢不夠', '钱不够', '錢不夠', '太贵了', '太貴了',
            '便宜一点', '便宜一點', '便宜的', '实惠的', '實惠的', '划算的', '劃算的'
        ]

        for pattern in price_sentiment_patterns:
            if pattern in user_input:
                self.current_search_context['price_preference'] = 'cheaper'
                break

        # 提取地点信息
        location_patterns = [
            r'(北京|上海|广州|深圳|杭州|南京|苏州|成都|重庆|武汉|西安|天津|青岛|大连|厦门|宁波|无锡|佛山|东莞|长沙|郑州|济南|哈尔滨|福州|昆明|南昌|贵阳|石家庄|南宁|海口|兰州|银川|西宁|乌鲁木齐|拉萨|呼和浩特|太原|沈阳|长春|合肥)',
            r'(江苏|浙江|广东|山东|河南|湖北|湖南|四川|福建|安徽|河北|山西|陕西|辽宁|吉林|黑龙江|江西|云南|贵州|广西|海南|甘肃|青海|宁夏|新疆|西藏|内蒙古)'
        ]

        for pattern in location_patterns:
            match = re.search(pattern, user_input)
            if match:
                self.current_search_context['location'] = match.group(1)
                break

        # 提取品类信息
        category_keywords = {
            '食品': ['糕点', '点心', '零食', '小食', '美食', '食品', '吃的'],
            '服装': ['衣服', '上衣', '裤子', '裙子', '外套', 'T恤', '衬衫'],
            '数码': ['手机', '电脑', '平板', '耳机', '音响', '充电器'],
            '家居': ['家具', '床', '沙发', '桌子', '椅子', '灯具'],
            '美妆': ['化妆品', '护肤品', '口红', '面膜', '洗面奶'],
            '运动': ['运动鞋', '运动服', '健身器材', '球类', '户外用品']
        }

        for category, keywords in category_keywords.items():
            for keyword in keywords:
                if keyword in user_input:
                    self.current_search_context['category'] = category
                    self.current_search_context['category_keyword'] = keyword
                    break
            if 'category' in self.current_search_context:
                break

    def get_search_context_for_rewrite(self) -> Dict[str, Any]:
        """获取用于query改写的搜索上下文"""
        context = {}

        # 获取最近的搜索相关信息
        recent_search_turns = [turn for turn in self.conversation_history[-5:]
                             if turn.intent == 'search']

        if recent_search_turns:
            # 合并所有搜索上下文信息
            for turn in recent_search_turns:
                context['previous_queries'] = context.get('previous_queries', [])
                context['previous_queries'].append(turn.user_input)

        # 添加当前搜索上下文
        context.update(self.current_search_context)

        return context

    def get_conversation_summary(self, last_n_turns: int = 3) -> str:
        """获取最近几轮对话的摘要"""
        if not self.conversation_history:
            return ""

        recent_turns = self.conversation_history[-last_n_turns:]
        summary_parts = []

        for turn in recent_turns:
            if turn.intent == 'search':
                summary_parts.append(f"用户搜索: {turn.user_input}")
                if turn.rewritten_query and turn.rewritten_query != turn.user_input:
                    summary_parts.append(f"改写为: {turn.rewritten_query}")
            elif turn.intent == 'interaction':
                summary_parts.append(f"用户交互: {turn.user_input}")

        return " | ".join(summary_parts)

load_dotenv()
cur_dir = Path(__file__).parent
load_dotenv("key.env")
SAMPLE_RATE = 48000

instruction = """
<Role>
You are a helpful shopping assistant that can search for products on Taobao.
"""

API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "27db1fc058c1861870be4c21a7f93cdc")
ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "https://idealab.alibaba-inc.com/api")
MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4-0409")

async def clear_queue(queue):
    while not queue.empty():
        try:
            item = await queue.get()
            queue.task_done()
        except asyncio.QueueEmpty:
            break

class SearchIntegratedAudioHandler(AsyncStreamHandler):
    """集成商品搜索功能的音频处理器"""

    def __init__(self) -> None:
        super().__init__(
            expected_layout="mono",
            output_sample_rate=SAMPLE_RATE,
            input_sample_rate=24000,
        )
        self.ws = None
        self.session = None
        self.output_queue = asyncio.Queue()
        self.llm_input_queue = asyncio.Queue()
        self.llm_output_queue = asyncio.Queue()
        self.voice_queue = asyncio.Queue()
        self.voice_inqueue = asyncio.Queue()

        # 创建聊天客户端
        self.chat = InterruptibleAzureOpenAI(api_key=API_KEY, endpoint=ENDPOINT, api_version='')

        # 创建TTS客户端
        self.tts_server = ByteDanceTTSStreaming(
            app_id = "4301754327" ,
            token = "iTyYQaoJGz1dNspdaXdBx0LltSwIHjF5",
            speaker='zh_female_shuangkuaisisi_moon_bigtts',
            audio_format='pcm',
        )

        # 创建搜索工具
        self.search_tool = TaobaoMainSearchTool()
        self.detail_tool = TaobaoItemDetailsTool()

        # 商品控制状态
        self.current_products = []
        self.current_product_index = 0
        self.last_search_query = ""

        # 商品批次管理状态
        self.all_cached_products = []  # 缓存所有搜索结果（20个左右）
        self.current_batch_index = 0   # 当前批次索引
        self.batch_size = 5           # 每批显示的商品数量
        self.total_batches = 0        # 总批次数

        # 状态提示控制 - 避免频繁显示"让我想想..."等提示
        self.last_status_time = {}  # 记录各类状态提示的最后显示时间
        self.status_cooldown = 30  # 状态提示冷却时间（秒）
        self.conversation_count = 0  # 对话轮次计数
        import time
        self.start_time = time.time()  # 会话开始时间

        # 对话记忆管理
        self.conversation_memory = ConversationMemory(max_turns=15)

        # 当前搜索结果的商品信息缓存
        self.current_search_results_info = {
            'products': [],
            'price_range': {'min': 0, 'max': 0},
            'avg_price': 0,
            'query': '',
            'timestamp': None
        }

    def copy(self):
        return SearchIntegratedAudioHandler()

    def should_show_status_message(self, status_type: str) -> bool:
        """判断是否应该显示状态提示消息"""
        import time
        current_time = time.time()

        # 增加对话计数
        self.conversation_count += 1

        # 在会话开始的前几轮对话中，可以显示状态提示
        if current_time - self.start_time < 60 and self.conversation_count <= 3:
            return True

        # 检查冷却时间
        if status_type in self.last_status_time:
            time_since_last = current_time - self.last_status_time[status_type]
            if time_since_last < self.status_cooldown:
                return False

        # 更新最后显示时间
        self.last_status_time[status_type] = current_time
        return True

    async def start_up(self):
        """连接到Azure实时音频API"""
        azure_api_key = "27db1fc058c1861870be4c21a7f93cdc"
        azure_endpoint = "wss://idealab.alibaba-inc.com/api/openai/realtime?model=gpt-4o-realtime-preview-1001"
        headers = {"api-key": azure_api_key}

        self.session = aiohttp.ClientSession()
        self.ws = await self.session.ws_connect(azure_endpoint, headers=headers)

        # 发送初始会话参数
        session_update_message = {
            "type": "session.update",
            "session":{
                "modalities": ["text", "audio"],
                "input_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "whisper-1",
                    "language": "zh"
                },
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.7,
                    "prefix_padding_ms": 100,
                    "silence_duration_ms": 500
                },
                "input_audio_noise_reduction": {
                    "type": "near_field"
                },
            }
        }
        await self.ws.send_str(json.dumps(session_update_message))

        # 配置流式处理参数
        self.stream_config = StreamConfig(
            trigger_strategy=TriggerStrategy.HYBRID,
            max_words_per_chunk=30,
            timeout_seconds=3.0,
            min_words_for_punctuation=5
        )

        # 启动异步任务
        asyncio.create_task(self.receive_messages())
        self.processer_task = asyncio.create_task(self.tts_server.run_stream_tts_processor(
            word_stream=self.voice_inqueue,
            voice_queue=self.voice_queue,
            config=self.stream_config,
            max_workers=1,
        ))

    async def interrupt(self, conv_id):
        """处理语音打断"""
        async def interrupt_llm(conv_id):
            if await self.chat.can_interrupt():
                self.clear_queue()
                success = await self.chat.interrupt()

        async def interrupt_tts(conv_id):
            interrupt_event = InterruptEvent()
            interrupt_event.type = "[interruption]"
            interrupt_event.id = conv_id
            interrupt_event.content = "[Conversation interrupted]"
            if self.tts_server.can_interrupt():
                print("tts runing打断")
                await clear_queue(self.voice_queue)
                await self.voice_queue.put(AdditionalOutputs(interrupt_event))
                await self.tts_server.interrupt()
                self.clear_queue()
                await self.voice_queue.put(AdditionalOutputs(interrupt_event))

        tasks = [interrupt_llm(conv_id), interrupt_tts(conv_id)]
        await asyncio.gather(*tasks)

        processer_task = await self.tts_server.resume(self.voice_inqueue, self.voice_queue)
        if processer_task != None:
            self.processer_task.cancel()
            self.processer_task = processer_task

    def clear_queue(self):
        """清空队列"""
        try:
            while not self.voice_queue.empty():
                self.voice_queue.get_nowait()
        except:
            pass

    async def receive_messages(self):
        """处理传入的WebSocket消息"""
        async for msg in self.ws:
            if msg.type == aiohttp.WSMsgType.TEXT:
                print("Received event:", msg.data)
                event = json.loads(msg.data)
                event_type = event.get("type")

                if event_type in ["final", "response.audio_transcript.done"]:
                    transcript = event.get("transcript", "")
                    class TranscriptEvent:
                        pass
                    te = TranscriptEvent()
                    te.transcript = transcript
                    await self.output_queue.put(AdditionalOutputs(te))

                elif event_type == "partial":
                    print("Partial transcript:", event.get("transcript", ""))

                elif event_type == "response.audio.delta":
                    audio_message = event.get("delta")
                    if audio_message:
                        try:
                            audio_bytes = base64.b64decode(audio_message)
                            audio_array = np.frombuffer(audio_bytes, dtype=np.int16)
                            audio_array = audio_array.reshape(1, -1)
                            await self.output_queue.put(
                                (self.output_sample_rate, audio_array)
                            )
                        except Exception as e:
                            print("Error processing audio data:", e)

                elif event_type == "input_audio_buffer.speech_started":
                    # 用户开始说话，降低播放音量到30%
                    print("🎤 检测到用户开始说话，降低播放音量")
                    volume_event = VolumeControlEvent()
                    volume_event.type = "volume_control"
                    volume_event.action = "reduce"
                    volume_event.volume = 0.3  # 降低到30%
                    volume_event.id = str(uuid.uuid4())
                    volume_event.content = "用户开始说话，降低音量"
                    await self.voice_queue.put(AdditionalOutputs(volume_event))

                elif event_type == "input_audio_buffer.speech_stopped":
                    # 用户停止说话，恢复播放音量到100%
                    print("🔇 检测到用户停止说话，恢复播放音量")
                    volume_event = VolumeControlEvent()
                    volume_event.type = "volume_control"
                    volume_event.action = "restore"
                    volume_event.volume = 1.0  # 恢复到100%
                    volume_event.id = str(uuid.uuid4())
                    volume_event.content = "用户停止说话，恢复音量"
                    await self.voice_queue.put(AdditionalOutputs(volume_event))

                elif event_type == "conversation.item.input_audio_transcription.completed":
                    transcript = event.get("transcript", "")

                    if is_punctuation_only(transcript) or transcript.strip() == '':
                        break

                    conv_id = str(uuid.uuid4())
                    await self.interrupt(conv_id=conv_id)

                    # 发送对话开始事件
                    interrupt_event = InterruptEvent()
                    interrupt_event.type = "[start]"
                    interrupt_event.id = conv_id
                    interrupt_event.content = "[Conversation start]"
                    await self.voice_queue.put(AdditionalOutputs(interrupt_event))

                    # 发送用户输入
                    te = ChatEvent()
                    te.content = "[user]:"+transcript
                    te.id = conv_id+"1"
                    te.type='msg'
                    await self.voice_queue.put(AdditionalOutputs(te))

                    # 处理搜索和LLM响应
                    asyncio.create_task(self.process_search_and_response(transcript, conv_id))

                else:
                    print("Unknown event:", event)
            elif msg.type == aiohttp.WSMsgType.ERROR:
                break

    async def process_search_and_response(self, transcript: str, conv_id: str):
        """处理搜索和LLM响应的核心方法 - 优化版本，使用单次LLM调用"""
        try:
            # 保存当前查询，用于传递给详情工具
            self.current_query = transcript

            # 打印当前对话记忆状态（调试用）
            self.print_conversation_memory_status()

            # 首先检查是否是商品控制指令
            control_result = await self.handle_product_control(transcript, conv_id)
            if control_result:
                return  # 如果是控制指令，直接返回

            # 使用统一的智能处理方法，一次LLM调用完成所有分析和处理
            await self.unified_intelligent_processing(transcript, conv_id)

        except Exception as e:
            print(f"❌ 搜索处理出错: {e}")
            import traceback
            traceback.print_exc()
            error_event = ChatEvent()
            error_event.content = f"抱歉，处理「{transcript}」时出现了问题，请稍后再试。"
            error_event.id = conv_id + "_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(error_event.content)

    async def unified_intelligent_processing(self, transcript: str, conv_id: str):
        """实时语音交互的智能处理方法 - 三部分响应 + 并行处理策略"""
        try:
            print(f"🧠 开始实时智能处理: {transcript}")

            # 获取对话上下文信息
            conversation_summary = self.conversation_memory.get_conversation_summary(last_n_turns=3)
            search_context = self.conversation_memory.get_search_context_for_rewrite()
            last_search_product = self._get_last_search_product()
            current_results_info = self.current_search_results_info
            has_current_products = bool(self.current_products and len(self.current_products) > 0)

            # 构建精简的实时处理提示
            realtime_prompt = f"""用户输入: "{transcript}"
对话历史: {conversation_summary if conversation_summary else "无"}
搜索上下文: {search_context if search_context else "无"}

分析用户意图并返回JSON:
- interaction: 问候/闲聊
- search_inquiry: 询问商品偏好
- search_execute: 明确搜索请求
- clarification: 需要澄清
- no_intent: 无意义内容

原则: 低延迟优先，精准判断，自然对话

JSON格式(严格按此格式):
{{
    "tts": "简洁的语音回复",
    "internal": "内部记录信息",
    "call": {{"name": "null"}},
    "intent": "interaction"
}}

如需搜索:
{{
    "tts": "正在搜索...",
    "internal": "执行搜索",
    "call": {{"name": "search_products", "query": "搜索词"}},
    "intent": "search_execute"
}}

直接返回JSON，无需解释："""

            # 使用LLM进行实时智能处理
            realtime_response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(realtime_prompt, model=MODEL_NAME):
                realtime_response += chunk

            print(f"🧠 实时处理原始回复: {realtime_response}")

            # 解析精简JSON响应
            try:
                import json
                # 尝试提取JSON部分
                json_start = realtime_response.find('{')
                json_end = realtime_response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = realtime_response[json_start:json_end]
                    compact_result = json.loads(json_str)
                else:
                    raise ValueError("未找到有效的JSON格式")

                print(f"✅ 精简处理结果: {compact_result}")

                # 转换为标准格式并执行
                processing_result = self.convert_compact_to_standard(compact_result, transcript)
                await self.execute_realtime_response(processing_result, transcript, conv_id)

            except Exception as parse_error:
                print(f"⚠️ 精简处理JSON解析失败: {parse_error}")
                # 如果解析失败，回退到简化处理
                await self.fallback_simple_processing(transcript, conv_id)

        except Exception as e:
            print(f"❌ 实时智能处理出错: {e}")
            import traceback
            traceback.print_exc()
            # 回退到简化处理
            await self.fallback_simple_processing(transcript, conv_id)

    def convert_compact_to_standard(self, compact_result: dict, transcript: str) -> dict:
        """将精简格式转换为标准格式"""
        try:
            # 提取精简字段
            tts_text = compact_result.get('tts', '')
            internal_text = compact_result.get('internal', '')
            call_info = compact_result.get('call', {})
            intent = compact_result.get('intent', 'interaction')

            # 处理call字段可能为None的情况
            if call_info is None:
                call_info = {}

            # 构建标准格式
            standard_result = {
                "tts_text": tts_text,
                "internal_text": internal_text,
                "function_call": {
                    "function_name": call_info.get('name', 'null') if isinstance(call_info, dict) else 'null',
                    "parameters": {
                        "query": call_info.get('query', transcript) if isinstance(call_info, dict) else transcript,
                        "original_query": transcript,
                        "rewritten_query": call_info.get('query', transcript) if isinstance(call_info, dict) else transcript
                    }
                },
                "intent": intent,
                "confidence": 0.9,  # 默认置信度
                "conversation_update": {
                    "intent": intent,
                    "user_input": transcript,
                    "agent_response": tts_text
                }
            }

            print(f"🔄 格式转换成功: 精简 -> 标准")
            return standard_result

        except Exception as e:
            print(f"❌ 格式转换失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回默认的交互响应
            return {
                "tts_text": "好的，我明白了。",
                "internal_text": "格式转换失败，使用默认响应",
                "function_call": {"function_name": "null"},
                "intent": "interaction",
                "confidence": 0.5,
                "conversation_update": {
                    "intent": "interaction",
                    "user_input": transcript,
                    "agent_response": "好的，我明白了。"
                }
            }

    async def execute_realtime_response(self, processing_result: dict, transcript: str, conv_id: str):
        """执行三部分并行处理策略"""
        try:
            print(f"🚀 执行实时响应策略")

            # 提取三部分响应
            tts_text = processing_result.get('tts_text', '')
            internal_text = processing_result.get('internal_text', '')
            function_call = processing_result.get('function_call', {})
            intent = processing_result.get('intent', 'interaction')
            conversation_update = processing_result.get('conversation_update', {})

            print(f"📢 TTS文本: {tts_text}")
            print(f"📝 内部文本: {internal_text}")
            print(f"🔧 函数调用: {function_call}")

            # 更新对话记忆
            if conversation_update:
                turn = ConversationTurn(
                    turn_id=conv_id,
                    user_input=conversation_update.get('user_input', transcript),
                    intent=conversation_update.get('intent', intent),
                    confidence=processing_result.get('confidence', 0.8),
                    agent_response=conversation_update.get('agent_response', tts_text)
                )
                self.conversation_memory.add_turn(turn)

            # 创建并行任务列表
            tasks = []

            # 任务1: 立即播报TTS文本（最高优先级）
            if tts_text:
                tasks.append(self.send_immediate_tts_response(tts_text, conv_id))

            # 任务2: 执行函数调用（如果需要）
            function_name = function_call.get('function_name')
            if function_name and function_name != 'null':
                if function_name == 'search_products':
                    # 商品搜索 - 在TTS播放的同时并行执行
                    parameters = function_call.get('parameters', {})
                    search_query = parameters.get('query', transcript)
                    original_query = parameters.get('original_query', transcript)
                    rewritten_query = parameters.get('rewritten_query', search_query)

                    tasks.append(self.execute_parallel_product_search(
                        search_query, original_query, rewritten_query, conv_id
                    ))

                elif function_name == 'web_search':
                    # 网络搜索 - 在TTS播放的同时并行执行
                    parameters = function_call.get('parameters', {})
                    web_query = parameters.get('query', transcript)

                    tasks.append(self.execute_parallel_web_search(web_query, conv_id))

            # 并行执行所有任务
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

            print(f"✅ 实时响应策略执行完成")

        except Exception as e:
            print(f"❌ 执行实时响应策略出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送错误回复
            await self.send_immediate_tts_response("抱歉，处理时出现了问题。", conv_id)

    async def send_immediate_tts_response(self, text: str, conv_id: str):
        """立即发送TTS响应，不等待其他处理"""
        try:
            print(f"🔊 立即播报: {text}")

            # 发送TTS事件
            tts_event = ChatEvent()
            tts_event.content = text
            tts_event.id = conv_id + "_tts"
            tts_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(tts_event))
            await self.voice_inqueue.put(text)

        except Exception as e:
            print(f"❌ 发送TTS响应出错: {e}")

    async def execute_parallel_product_search(self, search_query: str, original_query: str,
                                            rewritten_query: str, conv_id: str):
        """并行执行商品搜索，不阻塞TTS播放"""
        try:
            print(f"🔍 并行执行商品搜索: {search_query}")

            # 执行搜索
            search_result = self.search_tool.forward(search_query)
            print(f"🔍 搜索完成，结果类型: {type(search_result)}")

            if isinstance(search_result, list) and len(search_result) > 0:
                # 增强搜索结果（获取详情）
                enhanced_first_batch = await self.enhance_with_details(search_result[:5])
                if len(enhanced_first_batch) == 5 and len(search_result) > 5:
                    search_result = enhanced_first_batch + search_result[5:]
                else:
                    search_result = enhanced_first_batch

                # 处理搜索结果并发送到前端
                await self.process_search_results_parallel(search_result, search_query, conv_id)

            else:
                print(f"⚠️ 未找到搜索结果: {search_query}")

        except Exception as e:
            print(f"❌ 并行商品搜索出错: {e}")
            import traceback
            traceback.print_exc()

    async def execute_parallel_web_search(self, web_query: str, conv_id: str):
        """并行执行网络搜索，不阻塞TTS播放"""
        try:
            print(f"🌐 并行执行网络搜索: {web_query}")

            # 执行网络搜索
            enhanced_query = await self.enhance_query_with_time_context(web_query)
            search_results, _ = fetch_rag(enhanced_query)

            if search_results and len(search_results) > 0:
                # 格式化搜索结果
                voice_response = await self.format_web_search_response(web_query, search_results, enhanced_query)
                # 发送补充信息（不打断当前TTS）
                await self.send_supplementary_response(voice_response, conv_id)
            else:
                print(f"⚠️ 未找到网络搜索结果: {web_query}")

        except Exception as e:
            print(f"❌ 并行网络搜索出错: {e}")
            import traceback
            traceback.print_exc()

    async def send_supplementary_response(self, text: str, conv_id: str):
        """发送补充响应（在主要TTS完成后）"""
        try:
            print(f"📄 发送补充信息: {text}")

            # 发送补充信息事件
            supplement_event = ChatEvent()
            supplement_event.content = text
            supplement_event.id = conv_id + "_supplement"
            supplement_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(supplement_event))
            await self.voice_inqueue.put(text)

        except Exception as e:
            print(f"❌ 发送补充响应出错: {e}")

    async def process_search_results_parallel(self, search_result: list, query: str, conv_id: str):
        """并行处理搜索结果，不阻塞TTS播放"""
        try:
            print(f"📦 并行处理搜索结果: {len(search_result)} 个商品")

            # 分析搜索结果信息
            self.analyze_search_results_info(search_result, query)

            # 保存所有搜索结果到缓存
            self.all_cached_products = search_result
            self.last_search_query = query

            # 计算批次信息
            self.total_batches = (len(search_result) + self.batch_size - 1) // self.batch_size
            self.current_batch_index = 0

            # 设置当前显示的商品（第一批）
            first_batch_end = min(self.batch_size, len(search_result))
            first_batch = search_result[:first_batch_end]
            self.current_products = first_batch
            self.current_product_index = 0

            # 格式化搜索结果
            formatted_results = await self.format_search_results(first_batch, query)

            # 发送搜索结果到前端（不阻塞TTS）
            result_event = SearchResultEvent()
            result_event.content = formatted_results
            result_event.id = conv_id + "_search_results"
            result_event.type = 'search_results'
            result_event.raw_data = first_batch
            result_event.batch_info = {
                'current_batch': 1,
                'total_batches': self.total_batches,
                'batch_size': len(first_batch),
                'total_products': len(search_result)
            }
            await self.voice_queue.put(AdditionalOutputs(result_event))

            print(f"✅ 并行搜索结果处理完成: {len(search_result)} 个商品，显示前 {len(first_batch)} 个")

        except Exception as e:
            print(f"❌ 并行处理搜索结果出错: {e}")
            import traceback
            traceback.print_exc()

    async def execute_action_plan(self, processing_result: dict, transcript: str, conv_id: str):
        """根据统一处理结果执行相应的行动计划"""
        try:
            action_plan = processing_result.get('action_plan', {})
            action_type = action_plan.get('type', 'search')
            response_content = processing_result.get('response_content', '')

            print(f"🎯 执行行动计划: {action_type}")

            # 更新对话记忆
            conversation_update = processing_result.get('conversation_update', {})
            if conversation_update:
                turn = ConversationTurn(
                    turn_id=conv_id,
                    user_input=transcript,
                    intent=conversation_update.get('intent', processing_result.get('intent', 'unknown')),
                    confidence=processing_result.get('confidence', 0.8),
                    search_query=conversation_update.get('search_query', ''),
                    rewritten_query=conversation_update.get('rewritten_query', '')
                )
                self.conversation_memory.add_turn(turn)

            # 根据行动类型执行相应操作
            if action_type == 'ignore':
                # 无意图，直接忽略
                print(f"🔇 忽略无意图内容: {transcript}")
                return

            elif action_type == 'clarification':
                # 需要澄清
                clarification_question = action_plan.get('clarification_question', '您想要搜索什么商品呢？')
                await self.send_voice_response(clarification_question, conv_id)

            elif action_type == 'price_inquiry':
                # 价格询问
                price_question = action_plan.get('price_inquiry_question', '请问您希望价格在什么范围内呢？')
                await self.send_voice_response(price_question, conv_id)

            elif action_type == 'interaction':
                # 交互回复
                if response_content:
                    await self.send_voice_response(response_content, conv_id)

            elif action_type == 'web_search':
                # 网络搜索
                web_search_query = action_plan.get('web_search_query', transcript)
                await self.execute_web_search(web_search_query, response_content, conv_id)

            elif action_type == 'search':
                # 商品搜索
                if action_plan.get('execute_search', True):
                    search_query = action_plan.get('search_query', transcript)
                    optimized_query = processing_result.get('optimized_query', search_query)
                    await self.execute_product_search(search_query, optimized_query, response_content, conv_id)
                else:
                    # 只回复不搜索
                    if response_content:
                        await self.send_voice_response(response_content, conv_id)

            else:
                # 默认处理
                if response_content:
                    await self.send_voice_response(response_content, conv_id)

        except Exception as e:
            print(f"❌ 执行行动计划出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送默认回复
            await self.send_voice_response("好的，我明白了。", conv_id)

    async def fallback_simple_processing(self, transcript: str, conv_id: str):
        """简化的回退处理方法"""
        try:
            print(f"🔄 使用简化处理: {transcript}")

            # 简单的关键词匹配判断意图
            text = transcript.strip().lower()

            # 检查是否是无意图
            if self._is_no_intent(transcript):
                print(f"🔇 检测到无意图内容，直接忽略: {transcript}")
                return

            # 检查是否需要澄清
            if self._is_unclear_input(transcript):
                await self.send_voice_response("抱歉，我没有听清楚，您能再说一遍吗？", conv_id)
                return

            # 检查是否是交互意图
            interaction_keywords = ['你好', '谢谢', '再见', '天气', '时间', '什么是', '怎么样']
            if any(keyword in text for keyword in interaction_keywords):
                await self.send_voice_response("很高兴为您服务！有什么可以帮您的吗？", conv_id)
                return

            # 默认按搜索处理
            await self.execute_product_search(transcript, transcript, f"正在为您搜索「{transcript}」相关商品...", conv_id)

        except Exception as e:
            print(f"❌ 简化处理出错: {e}")
            await self.send_voice_response("抱歉，我刚才走神了，您能再说一遍吗？", conv_id)

    async def send_voice_response(self, content: str, conv_id: str):
        """发送语音回复"""
        try:
            response_event = ChatEvent()
            response_event.content = content
            response_event.id = conv_id + "_response"
            response_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(response_event))
            await self.voice_inqueue.put(content)
        except Exception as e:
            print(f"❌ 发送语音回复出错: {e}")

    async def execute_web_search(self, query: str, status_message: str, conv_id: str):
        """执行网络搜索"""
        try:
            print(f"🌐 执行网络搜索: {query}")

            # 发送状态消息
            if status_message:
                await self.send_voice_response(status_message, conv_id)

            # 执行搜索
            enhanced_query = await self.enhance_query_with_time_context(query)
            search_results, _ = fetch_rag(enhanced_query)

            if search_results and len(search_results) > 0:
                # 格式化搜索结果
                voice_response = await self.format_web_search_response(query, search_results, enhanced_query)
                await self.send_voice_response(voice_response, conv_id)
            else:
                await self.send_voice_response(f"抱歉，没有找到关于「{query}」的相关信息。", conv_id)

        except Exception as e:
            print(f"❌ 网络搜索执行出错: {e}")
            await self.send_voice_response(f"抱歉，搜索「{query}」时出现了问题。", conv_id)

    async def execute_product_search(self, original_query: str, optimized_query: str, status_message: str, conv_id: str):
        """执行商品搜索"""
        try:
            print(f"🔍 执行商品搜索: {original_query} -> {optimized_query}")

            # 发送状态消息
            if status_message:
                await self.send_voice_response(status_message, conv_id)

            # 如果查询被优化，播报优化后的查询
            if optimized_query != original_query:
                rewrite_announcement = f"我理解您要搜索「{optimized_query}」，正在为您查找相关商品..."
                await self.send_voice_response(rewrite_announcement, conv_id)

            # 执行搜索
            try:
                search_result = self.search_tool.forward(optimized_query)
                print(f"🔍 搜索完成，结果类型: {type(search_result)}")

                if isinstance(search_result, list) and len(search_result) > 0:
                    # 增强搜索结果
                    enhanced_first_batch = await self.enhance_with_details(search_result[:5])
                    if len(enhanced_first_batch) == 5 and len(search_result) > 5:
                        search_result = enhanced_first_batch + search_result[5:]
                    else:
                        search_result = enhanced_first_batch

                    # 处理搜索结果
                    await self.process_search_results(search_result, optimized_query, conv_id)
                else:
                    await self.send_voice_response(f"抱歉，没有找到「{optimized_query}」相关的商品。", conv_id)

            except Exception as search_error:
                print(f"❌ 商品搜索API调用失败: {search_error}")
                await self.send_voice_response(f"抱歉，搜索「{optimized_query}」时出现了问题。", conv_id)

        except Exception as e:
            print(f"❌ 商品搜索执行出错: {e}")
            await self.send_voice_response(f"抱歉，处理搜索请求时出现了问题。", conv_id)

    async def process_search_results(self, search_result: list, query: str, conv_id: str):
        """处理搜索结果并发送给前端"""
        try:
            print(f"📦 处理搜索结果: {len(search_result)} 个商品")

            # 分析搜索结果信息
            self.analyze_search_results_info(search_result, query)

            # 保存所有搜索结果到缓存
            self.all_cached_products = search_result
            self.last_search_query = query

            # 计算批次信息
            self.total_batches = (len(search_result) + self.batch_size - 1) // self.batch_size
            self.current_batch_index = 0

            # 设置当前显示的商品（第一批）
            first_batch_end = min(self.batch_size, len(search_result))
            first_batch = search_result[:first_batch_end]
            self.current_products = first_batch
            self.current_product_index = 0

            # 格式化搜索结果
            formatted_results = await self.format_search_results(first_batch, query)

            # 发送搜索结果到前端
            result_event = SearchResultEvent()
            result_event.content = formatted_results
            result_event.id = conv_id + "_search_results"
            result_event.type = 'search_results'
            result_event.raw_data = first_batch
            result_event.batch_info = {
                'current_batch': 1,
                'total_batches': self.total_batches,
                'batch_size': len(first_batch),
                'total_products': len(search_result)
            }
            await self.voice_queue.put(AdditionalOutputs(result_event))

            # 生成语音摘要
            voice_summary = await self.generate_voice_summary(first_batch, query)
            total_count = len(search_result)
            current_batch_count = len(first_batch)
            batch_info = f"共找到{total_count}个商品，当前显示前{current_batch_count}个" if total_count > current_batch_count else f"找到{total_count}个商品"

            # 发送语音摘要
            summary_response = f"{voice_summary}。{batch_info}。"
            await self.send_voice_response(summary_response, conv_id)

            print(f"✅ 搜索结果处理完成: {len(search_result)} 个商品，显示前 {len(first_batch)} 个")

        except Exception as e:
            print(f"❌ 处理搜索结果出错: {e}")
            import traceback
            traceback.print_exc()
            await self.send_voice_response(f"找到了一些商品，但显示时出现了问题。", conv_id)

    async def analyze_user_intent(self, transcript: str, conv_id: str):
        """分析用户意图 - 已弃用，保留用于兼容性"""
        try:
            print(f"🧠 开始分析用户意图: {transcript}")

            # 首先检查是否是无意图（直接忽略）
            if self._is_no_intent(transcript):
                return {
                    "intent": "no_intent",
                    "confidence": 0.95,
                    "reason": "无意义杂音或环境音，直接忽略",
                    "keywords": []
                }

            # 然后检查是否是无法理解的输入（需要反问）
            if self._is_unclear_input(transcript):
                return {
                    "intent": "unclear",
                    "confidence": 0.9,
                    "reason": "语音识别内容无法理解",
                    "keywords": []
                }

            # 获取对话上下文用于意图分析
            conversation_summary = self.conversation_memory.get_conversation_summary(last_n_turns=3)
            search_context = self.conversation_memory.get_search_context_for_rewrite()

            # 检查是否应该询问价格偏好
            price_inquiry_info = await self.should_ask_price_preference(transcript)
            if price_inquiry_info.get('should_ask'):
                print(f"💰 检测到价格不满意，准备询问价格偏好")
                return {
                    "intent": "price_inquiry",
                    "confidence": 0.9,
                    "reason": "用户对当前商品价格不满意，需要询问具体价格偏好",
                    "keywords": ["价格", "太贵"],
                    "price_info": price_inquiry_info
                }

            # 构建包含上下文的意图分析提示
            intent_prompt = f"""
            请分析用户输入的意图类型。

            当前用户输入："{transcript}"

            对话历史摘要：{conversation_summary if conversation_summary else "无"}

            当前搜索上下文：{search_context if search_context else "无"}

            意图分类：
            1. no_intent - 无意图：**仅限于**真正的无意义内容，如：
               - 纯语气词："唉"、"啊"、"嗯嗯"、"呃呃"
               - 环境杂音、咳嗽声、重复字符
               - 明显在和其他人说话："妈妈你过来"、"老公你在哪"
               - **注意**：单独的商品名称（如"手机"）绝不是无意图！

            2. search - 搜索意图：**这是语音购物助手的核心功能，应该优先识别**，包括：
               - **任何商品名称**："手机"、"衣服"、"苹果"、"电脑"、"鞋子"、"雨伞"等
               - **品牌名称**："华为"、"耐克"、"苹果"、"小米"等
               - **商品属性**："红色的"、"大一点的"、"便宜的"、"好看的"
               - **价格相关**："4000以下的"、"便宜一点的"、"我的钱不够"、"太贵了"
               - **购买意图**："我想买"、"帮我找"、"看看"、"搜索"、"需要"
               - **隐含购买需求**：**重要！即使没有明确的上下文也要识别**，如：
                 * "我昨天丢了伞"、"手机坏了"、"充电器没有了" → **明确的购买意图**
                 * "我的X丢了"、"X坏了"、"X用完了"、"缺少X" → **明确的购买意图**
                 * 在天气相关对话后提到"丢了伞"、"没有伞"等 → 明显的购买意图
                 * 在讨论某类商品后提到"坏了"、"用完了"、"不够用" → 购买意图
                 * 表达缺失或需要替换的物品 → 购买意图
               - **对之前搜索的补充**：如果有搜索历史，后续的属性描述都是搜索意图

            3. interaction - 交互意图：**包括所有需要信息查询而非商品购买的对话**，如：
               - **问候语**："你好"、"谢谢"、"再见"
               - **天气查询**："今天天气怎么样"、"会下雨吗"
               - **时间查询**："现在几点"、"今天星期几"
               - **百科知识**："什么是人工智能"、"北京有多少人"
               - **纯闲聊**："你叫什么名字"、"你是谁"
               - **🚨 重要：攻略和信息查询**：
                 * "故宫的游玩攻略"、"北京旅游攻略"、"杭州美食攻略"
                 * "网上搜索来的攻略"、"在线攻略"、"旅游指南"
                 * "景点介绍"、"路线规划"、"交通指南"
                 * "怎么去XX"、"XX在哪里"、"XX开放时间"
                 * 任何明确要求"网上搜索"、"在线查询"的内容

            4. ambiguous - 模糊意图：**极少使用**，仅当真正无法判断时
            5. need_clarification - 需要澄清：**极少使用**，仅当输入完全无法理解时

            **核心判断原则**：

            1. **商品名称 = 搜索意图**：
               - 任何可能是商品的词汇都应该识别为search意图
               - 例如："手机"、"电脑"、"衣服"、"鞋子"、"包"、"书"等
               - 品牌名称也是搜索意图："苹果"、"华为"、"耐克"、"阿迪"等

            2. **上下文优先原则**：
               - 如果用户之前搜索过商品，后续输入优先考虑为search意图
               - 例如：历史有"手机" + 当前"红色的" = search意图（红色手机）
               - 例如：历史有"运动鞋" + 当前"太贵了" = search意图（便宜的运动鞋）

            3. **宽松的搜索意图识别**：
               - 价格相关：**任何**价格表达都是search意图
                 "我的钱不够"、"太贵了"、"便宜一点"、"4000以下的"、"不超过X元"
               - 属性相关：**任何**商品属性都是search意图
                 "红色的"、"大一点的"、"好看的"、"质量好的"、以"的"结尾的描述
               - 购买意图：**任何**购买相关表达都是search意图
                 "我想买"、"帮我找"、"搜索"、"看看"、"需要"、"要"、"找"

            4. **上下文相关的购买意图识别**：
               - **重要**：充分利用对话历史判断隐含购买需求
               - 如果用户之前询问了天气（特别是下雨相关），后续提到"伞"相关内容 → search意图
               - 如果用户之前搜索了某类商品，后续提到"坏了"、"丢了"、"没有了" → search意图
               - 如果对话上下文暗示了某种需求，相关的物品提及都应视为search意图
               - **示例**：
                 * 对话历史："杭州今天会下雨吗" + 当前输入："我昨天丢了伞" → **明确的search意图**
                 * 对话历史："手机推荐" + 当前输入："我的坏了" → **明确的search意图**

            5. **严格限制no_intent**：
               - **绝对不要**将商品名称判断为no_intent
               - **绝对不要**将品牌名称判断为no_intent
               - **绝对不要**将购买相关词汇判断为no_intent
               - **绝对不要**将可能与上下文相关的物品提及判断为no_intent
               - 只有真正的杂音、语气词、环境音才是no_intent

            6. **🚨 重要：正确识别interaction意图**：
               - **攻略和信息查询必须是interaction意图**，不是search意图
               - 攻略类："故宫的游玩攻略"、"北京旅游攻略"、"杭州美食攻略"
               - 信息查询："网上搜索来的攻略"、"在线攻略"、"旅游指南"
               - 地点查询："怎么去XX"、"XX在哪里"、"XX开放时间"
               - 问候："你好"、"谢谢"、"再见"
               - 天气查询："今天天气怎么样"（但不包括天气相关的购买需求）
               - 时间："现在几点"
               - 百科："什么是人工智能"
               - **关键判断**：如果用户明确提到"网上搜索"、"攻略"、"指南"等，必须是interaction意图

            7. **避免过度澄清**：
               - **优先选择search意图**，而不是need_clarification
               - 只有完全无法理解的乱码才使用need_clarification
               - 宁可误判为search也不要误判为no_intent
               - **特别注意**：结合上下文的隐含购买需求不应被误判为澄清需求

            请只返回JSON格式：
            {{
                "intent": "no_intent|search|interaction|ambiguous|need_clarification",
                "confidence": 0.0-1.0,
                "reason": "判断理由",
                "keywords": ["关键词1", "关键词2"]
            }}
            """

            # 使用流式聊天获取意图分析结果
            intent_response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(intent_prompt, model=MODEL_NAME):
                intent_response += chunk

            print(f"🧠 意图分析原始回复: {intent_response}")

            # 解析JSON响应
            try:
                import json
                # 尝试提取JSON部分
                json_start = intent_response.find('{')
                json_end = intent_response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = intent_response[json_start:json_end]
                    intent_result = json.loads(json_str)
                else:
                    raise ValueError("未找到有效的JSON格式")

                print(f"✅ 意图分析结果: {intent_result}")
                return intent_result

            except Exception as parse_error:
                print(f"⚠️ 意图分析JSON解析失败: {parse_error}")
                # 如果解析失败，使用简单的关键词匹配作为备选
                return await self.fallback_intent_analysis(transcript)

        except Exception as e:
            print(f"❌ 意图分析出错: {e}")
            # 出错时默认为搜索意图
            return {
                "intent": "search",
                "confidence": 0.5,
                "reason": "意图分析出错，默认为搜索意图",
                "keywords": [transcript]
            }

    async def fallback_intent_analysis(self, transcript: str):
        """备选的意图分析方法（基于关键词匹配）"""
        try:
            # 清理输入
            text = transcript.strip().lower()

            # 首先检查是否是无意图（直接忽略）
            if self._is_no_intent(transcript):
                return {
                    "intent": "no_intent",
                    "confidence": 0.95,
                    "reason": "无意义杂音或环境音，直接忽略",
                    "keywords": []
                }

            # 然后检查是否需要澄清
            if self._is_unclear_input(transcript):
                return {
                    "intent": "unclear",
                    "confidence": 0.9,
                    "reason": "输入内容无法理解",
                    "keywords": []
                }

            # 获取对话上下文
            last_search_product = self._get_last_search_product()
            has_recent_search = bool(last_search_product)

            # 如果有最近的搜索历史，优先考虑搜索相关意图
            if has_recent_search:
                # 价格相关表达（在有搜索历史的情况下，这些都应该是搜索意图）
                price_related_keywords = [
                    '钱不够', '錢不夠', '太贵', '太貴', '便宜', '实惠', '實惠', '划算', '劃算',
                    '以下', '以内', '以內', '不超过', '不超過', '低于', '低於'
                ]

                for keyword in price_related_keywords:
                    if keyword in text:
                        return {
                            "intent": "search",
                            "confidence": 0.9,
                            "reason": f"包含价格相关关键词且有搜索历史: {keyword}",
                            "keywords": [keyword]
                        }

                # 属性相关表达（颜色、品牌、规格等）
                import re
                attribute_patterns = [
                    '的$', '色', '牌', '大', '小', '新', '旧', '舊'
                ]

                for pattern in attribute_patterns:
                    if re.search(pattern, text):
                        return {
                            "intent": "search",
                            "confidence": 0.85,
                            "reason": f"包含属性关键词且有搜索历史: {pattern}",
                            "keywords": [pattern]
                        }

            # 检查是否过于简短或模糊，需要澄清
            if len(text) <= 2 or text in ['嗯', '啊', '哦', '额', '那个', '这个', '就是', '然后']:
                # 如果有搜索历史，简短输入可能是属性补充，降低澄清阈值
                if has_recent_search:
                    return {
                        "intent": "search",
                        "confidence": 0.6,
                        "reason": "简短输入但有搜索历史，可能是属性补充",
                        "keywords": [text]
                    }
                else:
                    return {
                        "intent": "need_clarification",
                        "confidence": 0.8,
                        "reason": "输入过于简短或模糊，需要澄清",
                        "keywords": [text]
                    }

            # 交互意图关键词
            interaction_keywords = [
                # 问候语
                '你好', '您好', 'hello', 'hi', '早上好', '下午好', '晚上好',
                # 天气相关
                '天气', '温度', '下雨', '晴天', '阴天', '雪', '风',
                # 百科知识
                '什么是', '怎么样', '为什么', '如何', '怎么', '介绍一下',
                # 闲聊
                '聊天', '无聊', '心情', '感觉', '觉得',
                # 时间相关
                '现在几点', '今天', '明天', '昨天', '时间',
                # 攻略和信息查询（重要！）
                '攻略', '指南', '路线', '景点', '旅游', '旅遊', '游玩', '遊玩',
                '网上搜索', '在线查询', '网络搜索', '搜索攻略',
                '怎么去', '在哪里', '在哪裡', '开放时间', '营业时间',
                '交通', '住宿', '美食', '特色', '推荐景点'
            ]

            # 检查是否包含交互意图关键词
            for keyword in interaction_keywords:
                if keyword in text:
                    return {
                        "intent": "interaction",
                        "confidence": 0.8,
                        "reason": f"包含交互关键词: {keyword}",
                        "keywords": [keyword]
                    }

            # 明确的商品搜索关键词 - 大幅扩展
            search_keywords = [
                # 购买动作词
                '买', '购买', '搜索', '找', '要', '需要', '想要', '帮我找', '给我找', '看看', '选择',
                '购物', '下单', '订购', '采购', '挑选', '比较', '推荐',

                # 电子产品
                '手机', '电脑', '笔记本', '平板', '耳机', '音响', '充电器', '数据线', '键盘', '鼠标',
                '相机', '摄像头', '电视', '投影仪', '路由器', '硬盘', '内存', '显卡', '主板',

                # 服装鞋帽
                '衣服', '鞋子', '包包', '帽子', '裤子', '裙子', '外套', 'T恤', '衬衫', '毛衣',
                '内衣', '袜子', '围巾', '手套', '皮带', '领带', '运动鞋', '高跟鞋', '靴子',

                # 美妆护肤
                '化妆品', '护肤品', '面膜', '洗面奶', '爽肤水', '精华', '乳液', '防晒', '口红',
                '粉底', '眼影', '睫毛膏', '香水', '洗发水', '护发素', '沐浴露', '牙膏', '牙刷',

                # 食品饮料
                '食品', '零食', '饮料', '茶叶', '咖啡', '酒', '牛奶', '酸奶', '面包', '饼干',
                '巧克力', '糖果', '坚果', '水果', '蔬菜', '肉类', '海鲜', '调料', '米', '面',

                # 家居用品
                '家电', '冰箱', '洗衣机', '空调', '电视机', '微波炉', '电饭煲', '热水器', '吸尘器',
                '床', '沙发', '桌子', '椅子', '衣柜', '书架', '台灯', '窗帘', '地毯', '枕头',
                '被子', '床单', '毛巾', '杯子', '碗', '盘子', '锅', '刀', '筷子', '勺子',

                # 运动户外
                '运动', '健身', '跑步', '游泳', '篮球', '足球', '羽毛球', '乒乓球', '瑜伽', '登山',
                '自行车', '滑板', '轮滑', '帐篷', '睡袋', '背包', '运动服', '运动鞋',

                # 母婴用品
                '奶粉', '尿布', '婴儿车', '玩具', '童装', '童鞋', '学习用品', '文具',

                # 汽车用品
                '汽车', '轮胎', '机油', '车载', '导航', '行车记录仪', '车垫', '香水',

                # 图书文具
                '书', '小说', '教材', '杂志', '笔', '本子', '文件夹', '计算器', '尺子',

                # 宠物用品
                '宠物', '狗粮', '猫粮', '猫砂', '宠物玩具', '宠物用品',

                # 品牌名称
                '苹果', '华为', '小米', '三星', '联想', '戴尔', '惠普', '索尼', '佳能', '尼康',
                '耐克', '阿迪达斯', '优衣库', '宜家', '无印良品', '星巴克', '可口可乐',
                '宝马', '奔驰', '奥迪', '丰田', '本田', '大众'
            ]

            # 检查是否包含明确的搜索关键词
            for keyword in search_keywords:
                if keyword in text:
                    return {
                        "intent": "search",
                        "confidence": 0.8,
                        "reason": f"包含搜索关键词: {keyword}",
                        "keywords": [keyword]
                    }

            # 对于较短的输入，优先考虑为搜索意图而不是澄清
            # 只有在输入极短（≤2字符）且明显不是商品名称时才需要澄清
            if len(text) <= 2 and text not in ['手机', '电脑', '衣服', '鞋子', '包', '书', '车', '房', '表', '帽', '茶', '酒', '米', '面', '油', '盐', '药', '笔', '纸', '杯', '碗', '锅', '床', '椅', '桌', '灯']:
                return {
                    "intent": "need_clarification",
                    "confidence": 0.7,
                    "reason": "输入过短且不是明确的商品名称，需要澄清",
                    "keywords": [text]
                }

            # 对于3-5字符的输入，如果有搜索历史则优先为搜索意图
            if len(text) <= 5 and has_recent_search:
                return {
                    "intent": "search",
                    "confidence": 0.7,
                    "reason": "输入较短但有搜索历史，可能是商品属性补充",
                    "keywords": [text]
                }

            # 默认为搜索意图（提高置信度，因为这是购物助手）
            return {
                "intent": "search",
                "confidence": 0.75,
                "reason": "作为购物助手，默认将未明确分类的输入视为搜索意图",
                "keywords": [transcript]
            }

        except Exception as e:
            print(f"❌ 备选意图分析出错: {e}")
            return {
                "intent": "need_clarification",
                "confidence": 0.5,
                "reason": "备选分析出错，需要澄清用户意图",
                "keywords": [transcript]
            }

    def _is_no_intent(self, transcript: str):
        """检查是否为无意图（直接忽略的内容）- 更加保守的判断"""
        try:
            # 清理输入
            text = transcript.strip().lower()

            # 检查是否为空或只有空白字符
            if not text or len(text.strip()) == 0:
                return True

            # 无意义的短词汇（直接忽略）- 更加严格的列表
            meaningless_short_words = [
                # 纯语气词（不包含任何商品或动作含义）
                '唉', '啊', '哦', '嗯', '呃', '额', '诶', '咦', '哎',
                # 单字符无意义内容
                '的', '了', '呢', '吧', '嘛', '哈', '呵', '嘿',
                # 常见杂音
                '咳', '嗯嗯', '啊啊', '哦哦', '呃呃',
                # 标点符号
                '？', '?', '。', '.', '，', ',', '！', '!', '；', ';', '：', ':',
                '…', '...', '——', '--',
            ]

            # 检查是否是无意义短词
            if text in meaningless_short_words:
                return True

            # 检查是否只包含标点符号和空格
            import string
            if all(c in string.punctuation + ' \t\n\r' for c in text):
                return True

            # 检查是否是特别短的无意义内容（1-2个字符且不是有意义的词）
            if len(text) <= 2:
                # 有意义的短词（不应该忽略）- 大幅扩展商品和交互相关词汇
                meaningful_short_words = [
                    # 动作词
                    '买', '要', '找', '搜', '看', '好', '行', '是', '对', '不', '来', '去', '给',
                    # 常见商品类别
                    '手机', '电脑', '衣服', '鞋子', '包', '书', '车', '房', '表', '帽', '裤', '裙',
                    '茶', '酒', '米', '面', '油', '盐', '糖', '醋', '肉', '菜', '果', '奶',
                    '药', '笔', '纸', '杯', '碗', '锅', '刀', '床', '椅', '桌', '灯', '扇', '伞',
                    # 品牌简称
                    '苹果', '华为', '小米', '耐克', '阿迪', '优衣库', '宝马', '奔驰',
                    # 交互词汇
                    '你好', '谢谢', '再见', '帮忙', '请问', '什么', '哪里', '怎么', '为什么',
                    '天气', '时间', '今天', '明天', '现在', '早上', '晚上',
                    # 数字和量词
                    '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '个', '只', '件', '双',
                    # 状态词（可能暗示购买需求）
                    '丢', '坏', '没', '缺', '少', '完', '空'
                ]

                # 如果不在有意义短词列表中，且长度很短，认为是无意图
                if text not in meaningful_short_words:
                    return True

            # 检查是否明显在和其他人说话（包含称呼或对话特征）
            talking_to_others_patterns = [
                # 对其他人的称呼
                '妈妈', '爸爸', '老公', '老婆', '宝贝', '亲爱的', '儿子', '女儿',
                '同事', '朋友', '老师', '同学', '老板', '客户',
                # 明显的对话内容
                '你过来', '你去', '你看', '你听', '你说什么', '你在干嘛',
                '我们走', '我们去', '咱们', '大家', '他们', '她们',
                # 日常对话
                '吃饭了', '睡觉了', '上班了', '下班了', '回家了',
                '在干嘛', '怎么了', '没事吧',
                # 电话对话特征
                '喂', '听得到吗', '信号不好', '你在哪', '忙吗',
            ]

            # 检查是否包含对话特征
            for pattern in talking_to_others_patterns:
                if pattern in text:
                    return True

            # 检查是否是环境杂音的特征（包含多个重复字符或无意义组合）
            # 如：aaa, 呃呃呃, 嗯嗯嗯等
            if len(set(text)) == 1 and len(text) >= 2:  # 同一字符重复
                return True

            # 检查是否包含过多无意义字符组合
            noise_chars = ['呃', '嗯', '啊', '哦', '额', '唉', '咳', '嘿', '哈']
            noise_count = sum(1 for char in noise_chars if char in text)
            if noise_count >= len(text) * 0.7:  # 70%以上是杂音字符
                return True

            # 检查是否包含可能的商品或购买相关词汇（即使很短也不应忽略）
            potential_purchase_keywords = [
                # 商品相关
                '伞', '手机', '电脑', '衣服', '鞋', '包', '书', '车', '房', '表', '帽',
                '茶', '酒', '米', '面', '油', '盐', '糖', '醋', '肉', '菜', '果', '奶',
                '药', '笔', '纸', '杯', '碗', '锅', '刀', '床', '椅', '桌', '灯', '扇',
                # 明确的购买意图词汇
                '需要', '要买', '想买', '买个', '买点', '找找', '看看',
                # 品牌
                '苹果', '华为', '小米', '耐克', '阿迪', '宝马', '奔驰'
            ]

            # 如果包含任何购买相关词汇，不应被判断为无意图
            for keyword in potential_purchase_keywords:
                if keyword in text:
                    return False

            # 状态词（如"丢了"、"坏了"等）单独出现时不应该是no_intent，应该让LLM进一步判断
            # 这些词汇可能暗示购买需求，但需要更多上下文信息
            status_keywords = ['丢了', '坏了', '没了', '用完', '缺少', '不够', '不见', '找不到', '丢', '坏', '没', '缺', '少', '完', '空']
            for keyword in status_keywords:
                if keyword in text:
                    return False  # 不判断为无意图，让LLM进一步分析

            return False

        except Exception as e:
            print(f"❌ 检查无意图时出错: {e}")
            return False

    def _is_unclear_input(self, transcript: str):
        """检查输入是否无法理解"""
        try:
            # 清理输入
            text = transcript.strip().lower()

            # 检查是否为空或只有空白字符
            if not text or len(text.strip()) == 0:
                return True

            # 检查是否只包含标点符号
            import string
            if all(c in string.punctuation + ' \t\n\r' for c in text):
                return True

            # 检查是否包含可能的商品或购买相关词汇（即使很短也不应判断为unclear）
            potential_meaningful_keywords = [
                # 商品相关
                '伞', '手机', '电脑', '衣服', '鞋', '包', '书', '车', '房', '表', '帽',
                '茶', '酒', '米', '面', '油', '盐', '糖', '醋', '肉', '菜', '果', '奶',
                '药', '笔', '纸', '杯', '碗', '锅', '刀', '床', '椅', '桌', '灯', '扇',
                # 品牌
                '苹果', '华为', '小米', '耐克', '阿迪', '宝马', '奔驰',
                # 动作词
                '买', '要', '找', '搜', '看', '好', '行', '是', '对', '不', '来', '去', '给',
                # 交互词汇
                '你好', '谢谢', '再见', '帮忙', '请问', '什么', '哪里', '哪裡', '怎么', '怎麼', '为什么', '為什麼',
                '天气', '天氣', '时间', '時間', '今天', '明天', '现在', '現在', '早上', '晚上',
                # 地点查询
                '在哪里', '在哪裡', '在哪', '位置', '地址', '地点', '地點'
            ]

            # 如果包含任何有意义的词汇，不应被判断为unclear
            for keyword in potential_meaningful_keywords:
                if keyword in text:
                    return False

            # 检查是否包含过多无意义字符
            meaningless_chars = ['？', '?', '。', '.', '，', ',', '！', '!', '；', ';', '：', ':',
                               '…', '...', '呃', '嗯', '啊', '哦', '额', '那个', '这个', '就是']
            clean_text = text
            for char in meaningless_chars:
                clean_text = clean_text.replace(char, '')

            # 如果清理后长度太短，认为是无法理解的
            if len(clean_text.strip()) <= 1:
                return True

            # 检查是否是乱码或无意义字符串
            # 如果包含过多非中文、非英文、非数字字符，可能是乱码
            import re
            meaningful_chars = re.findall(r'[\u4e00-\u9fff\w\s]', text)
            if len(meaningful_chars) / len(text) < 0.5:  # 有意义字符占比低于50%
                return True

            return False

        except Exception as e:
            print(f"❌ 检查输入清晰度时出错: {e}")
            return False

    async def handle_unclear_input(self, transcript: str, conv_id: str):
        """处理无法理解的语音输入 - 已弃用，保留用于兼容性"""
        try:
            print(f"❓ 处理无法理解的输入: {transcript}")

            # 生成友好的反问回复
            clarification_responses = [
                "抱歉，我没有听清楚您说的话，能请您再说一遍吗？",
                "不好意思，刚才没听明白，您能再重复一下吗？",
                "抱歉，我没有理解您的意思，请您再说一次好吗？",
                "不好意思，您刚才说的我没听清，能再说一遍吗？",
                "抱歉，我没有听懂，您能重新说一下吗？"
            ]

            import random
            response = random.choice(clarification_responses)

            # 发送反问回复
            clarification_event = ChatEvent()
            clarification_event.content = response
            clarification_event.id = conv_id + "_unclear"
            clarification_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(clarification_event))
            await self.voice_inqueue.put(response)

            print(f"✅ 已发送反问回复: {response}")

        except Exception as e:
            print(f"❌ 处理无法理解输入时出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送默认回复
            default_response = "抱歉，我没有听清楚，您能再说一遍吗？"
            error_event = ChatEvent()
            error_event.content = default_response
            error_event.id = conv_id + "_unclear_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(default_response)

    async def handle_clarification_needed(self, transcript: str, conv_id: str, intent_result: dict):
        """处理需要澄清的模糊意图 - 已弃用，保留用于兼容性"""
        try:
            print(f"❓ 处理需要澄清的意图: {transcript}")

            # 立即发送处理状态消息，充分利用等待时间
            status_event = ChatEvent()
            status_event.content = "让我想想您的意思..."
            status_event.id = conv_id + "_clarification_status"
            status_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(status_event))
            await self.voice_inqueue.put(status_event.content)

            # 并行记录澄清需求到memory
            turn = ConversationTurn(
                turn_id=conv_id,
                user_input=transcript,
                intent='need_clarification',
                confidence=intent_result.get('confidence', 0.7)
            )
            self.conversation_memory.add_turn(turn)

            # 并行使用LLM生成针对性的澄清问题
            clarification_question = await self.generate_clarification_question(transcript, intent_result)

            # 发送澄清问题
            clarification_event = ChatEvent()
            clarification_event.content = clarification_question
            clarification_event.id = conv_id + "_clarification"
            clarification_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(clarification_event))
            await self.voice_inqueue.put(clarification_question)

            print(f"✅ 已发送澄清问题: {clarification_question}")

        except Exception as e:
            print(f"❌ 处理澄清需求时出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送默认澄清问题
            default_question = "您想要搜索什么商品呢？或者有什么其他需要帮助的吗？"
            error_event = ChatEvent()
            error_event.content = default_question
            error_event.id = conv_id + "_clarification_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(default_question)

    async def handle_price_inquiry(self, transcript: str, conv_id: str, intent_result: dict):
        """处理价格询问意图 - 已弃用，保留用于兼容性"""
        try:
            print(f"💰 处理价格询问意图: {transcript}")

            # 立即发送处理状态消息，充分利用等待时间
            status_event = ChatEvent()
            status_event.content = "我理解您对价格的关注，让我为您分析一下..."
            status_event.id = conv_id + "_price_status"
            status_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(status_event))
            await self.voice_inqueue.put(status_event.content)

            # 并行记录价格询问到memory
            turn = ConversationTurn(
                turn_id=conv_id,
                user_input=transcript,
                intent='price_inquiry',
                confidence=intent_result.get('confidence', 0.9)
            )
            self.conversation_memory.add_turn(turn)

            # 获取价格信息
            price_info = intent_result.get('price_info', {})

            # 并行生成价格询问问题
            price_question = await self.generate_price_inquiry(price_info)

            # 发送价格询问
            price_event = ChatEvent()
            price_event.content = price_question
            price_event.id = conv_id + "_price_inquiry"
            price_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(price_event))
            await self.voice_inqueue.put(price_question)

            print(f"✅ 已发送价格询问: {price_question}")

        except Exception as e:
            print(f"❌ 处理价格询问时出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送默认价格询问
            default_question = "请问您希望价格在什么范围内呢？"
            error_event = ChatEvent()
            error_event.content = default_question
            error_event.id = conv_id + "_price_inquiry_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(default_question)

    async def rewrite_search_query(self, original_query: str, conv_id: str) -> str:
        """改写搜索查询以提高搜索准确率 - 已弃用，保留用于兼容性"""
        try:
            print(f"🔄 开始改写搜索查询: {original_query}")

            # 获取对话上下文
            search_context = self.conversation_memory.get_search_context_for_rewrite()
            conversation_summary = self.conversation_memory.get_conversation_summary(last_n_turns=5)

            # 获取最近的搜索商品信息
            last_search_product = self._get_last_search_product()

            # 获取当前搜索结果的商品信息
            current_results_info = self.current_search_results_info
            price_suggestions = self.get_price_suggestion_based_on_results()

            print(f"🔍 对话摘要: {conversation_summary}")
            print(f"🔍 搜索上下文: {search_context}")
            print(f"🔍 最近搜索商品: {last_search_product}")
            print(f"🔍 当前搜索结果信息: {current_results_info.get('query', '无')} (价格范围: {current_results_info.get('price_range', {})})")
            print(f"🔍 价格建议: {price_suggestions}")

            # 构建query改写的LLM提示
            rewrite_prompt = f"""
            你是一个专业的商品搜索查询优化助手。请根据用户的当前输入、对话历史和搜索结果信息，改写搜索查询以提高搜索准确率。

            **重要：请特别注意歧义词汇的消歧处理！**

            当前用户输入："{original_query}"

            对话历史摘要：{conversation_summary if conversation_summary else "无"}

            最近搜索的商品：{last_search_product if last_search_product else "无"}

            **歧义词汇分析**：
            - 最近搜索词汇："{last_search_product if last_search_product else "无"}"
            - 当前输入："{original_query}"
            - 是否存在歧义消歧情况：需要判断

            当前搜索结果信息：
            """

            # 添加搜索结果信息
            if current_results_info.get('products'):
                rewrite_prompt += f"- 上次搜索查询：{current_results_info.get('query', '无')}\n"
                rewrite_prompt += f"- 搜索结果数量：{current_results_info.get('total_count', 0)}个商品\n"
                price_range = current_results_info.get('price_range', {})
                if price_range:
                    rewrite_prompt += f"- 价格范围：{price_range.get('min', 0):.0f}-{price_range.get('max', 0):.0f}元\n"
                    rewrite_prompt += f"- 平均价格：{current_results_info.get('avg_price', 0):.0f}元\n"

                if price_suggestions.get('has_cheaper_options'):
                    rewrite_prompt += f"- 建议更低价格：{', '.join(price_suggestions.get('lower_suggestions', []))}\n"

            rewrite_prompt += f"""

            搜索上下文信息：
            """

            # 添加上下文信息
            if search_context:
                if 'location' in search_context:
                    rewrite_prompt += f"- 地点：{search_context['location']}\n"
                if 'max_price' in search_context:
                    rewrite_prompt += f"- 价格上限：{search_context['max_price']}元\n"
                if 'price_range' in search_context:
                    price_range = search_context['price_range']
                    rewrite_prompt += f"- 价格范围：{price_range['min']}-{price_range['max']}元\n"
                if 'category' in search_context:
                    rewrite_prompt += f"- 商品类别：{search_context['category']}\n"
                if 'category_keyword' in search_context:
                    rewrite_prompt += f"- 类别关键词：{search_context['category_keyword']}\n"
                if 'previous_queries' in search_context:
                    recent_queries = search_context['previous_queries'][-3:]  # 最近3个查询
                    rewrite_prompt += f"- 最近查询：{', '.join(recent_queries)}\n"

            rewrite_prompt += f"""

            改写规则：

            **🚨 第一优先级：歧义词汇消歧（必须首先检查）**：

            **步骤1：检查是否存在歧义消歧情况**
            - 最近搜索词汇："{last_search_product if last_search_product else "无"}"
            - 当前输入："{original_query}"

            **步骤2：歧义词汇识别与默认倾向**
            常见歧义词汇及其**购物场景默认倾向**：
            - "苹果"：**默认倾向电子产品**（iPhone、iPad等），除非明确提到"水果"
            - "小米"：**默认倾向电子产品**（手机、电器等），除非明确提到"粮食"
            - "华为"：**默认倾向手机**，除非明确提到其他产品类别
            - "联想"：**默认倾向电脑**，除非明确提到其他产品类别
            - "海尔"：**默认倾向家电**，除非明确提到特定类别

            **步骤3：双向消歧逻辑**

            **3.1 正向消歧**（歧义词 + 类别澄清）：
            - 最近搜索："苹果" + 当前输入："水果" → "苹果水果"
            - 最近搜索："小米" + 当前输入："粮食" → "小米粮食"

            **3.2 反向消歧**（类别澄清 + 歧义词回溯）：
            - 最近搜索："苹果" + 当前输入："手机" → "苹果手机"
            - 最近搜索："小米" + 当前输入："电器" → "小米电器"
            - 最近搜索："华为" + 当前输入："路由器" → "华为路由器"

            **步骤4：智能默认处理**
            对于单独的歧义词汇（如用户只说"苹果"）：
            - 在购物场景中，优先选择商业价值更高的类别
            - "苹果" → 默认"苹果手机"（而不是"苹果水果"）
            - "小米" → 默认"小米手机"（而不是"小米粮食"）

            **步骤5：消歧改写规则**
            - 正向消歧：[歧义词汇] + [类别词汇]
            - 反向消歧：[歧义词汇] + [当前类别]
            - 默认处理：[歧义词汇] + [默认类别]

            2. **充分利用对话历史**：
               - 如果用户之前搜索过商品，当前输入很可能是对该商品的补充限制
               - 优先考虑与历史搜索的关联性

            3. **基于搜索结果的价格反馈处理**：
               - 如果有搜索结果信息且用户表达价格不满意，优先基于现有价格范围进行改写
               - "太贵了"、"这些都太贵了" → 根据当前价格范围建议更低价格
               - "便宜一点"、"有没有便宜一点的" → 使用建议的更低价格范围
               - "我的钱不够" → 结合搜索结果建议合适的价格范围

            4. **价格相关表达**：
               - "4000以下的"、"便宜的" → 添加价格限制到最近搜索的商品
               - 优先使用具体数字而不是模糊的"便宜"描述

            5. **属性补充**：
               - "红色的"、"大一点的"、"苹果的" → 添加属性到最近搜索的商品

            6. **智能合并**：将当前输入与最近搜索的商品信息合并成完整查询

            7. **保持自然**：改写后的查询要自然、简洁，适合商品搜索

            **🔥 关键示例（必须严格遵循）**：

            **正向消歧示例**（歧义词 → 类别澄清）：

            **示例1**：
            - 最近搜索："苹果" → 得到iPhone等电子产品结果
            - 用户说："我要买水果" → **必须改写为"苹果水果"**
            - ❌ 错误改写："水果"
            - ✅ 正确改写："苹果水果"

            **反向消歧示例**（歧义词 → 类别澄清）：

            **示例2（用户当前遇到的问题）**：
            - 用户说："我想买苹果" → **应该默认改写为"苹果手机"**（购物场景默认）
            - 用户澄清："我要的是手机" → **必须改写为"苹果手机"**（反向消歧）
            - ❌ 错误改写："手机"
            - ✅ 正确改写："苹果手机"

            **示例3**：
            - 最近搜索："小米" → 得到手机结果
            - 用户说："我要买电器" → **必须改写为"小米电器"**
            - ❌ 错误改写："电器"
            - ✅ 正确改写："小米电器"

            **默认处理示例**：

            **示例4**：
            - 用户说："苹果" → **默认改写为"苹果手机"**（而不是"苹果水果"）
            - 用户说："小米" → **默认改写为"小米手机"**（而不是"小米粮食"）

            **价格反馈示例**：
            - 搜索"运动鞋"得到价格范围200-800元，用户说"太贵了" → 改写为"200元以下运动鞋"
            - 搜索"iPhone手机"得到价格范围3000-8000元，用户说"我的钱不够" → 改写为"3000元以下iPhone手机"

            **属性补充示例**：
            - 最近搜索"手机"，用户说"苹果的" → 改写为"苹果手机"
            - 最近搜索"运动鞋"，用户说"耐克红色" → 改写为"耐克红色运动鞋"

            **🚨 绝对优先级规则**：

            **1. 歧义消歧检查（必须首先执行）**：

            **当前情况检查**：
            - 最近搜索："{last_search_product if last_search_product else "无"}"
            - 当前输入："{original_query}"

            **正向消歧**（歧义词 + 类别澄清）：
            - 如果最近搜索是"苹果"且当前输入包含"水果"相关词汇 → "苹果水果"
            - 如果最近搜索是"小米"且当前输入包含"粮食"相关词汇 → "小米粮食"

            **反向消歧**（类别澄清 + 歧义词回溯）：
            - 如果最近搜索是"苹果"且当前输入包含"手机"相关词汇 → "苹果手机"
            - 如果最近搜索是"小米"且当前输入包含"电器"相关词汇 → "小米电器"
            - 如果最近搜索是"华为"且当前输入包含"路由器"相关词汇 → "华为路由器"

            **默认处理**（单独歧义词）：
            - 用户只说"苹果" → 默认"苹果手机"（购物场景优先电子产品）
            - 用户只说"小米" → 默认"小米手机"（购物场景优先电子产品）

            **判断标准**：
            - 歧义词汇：苹果、小米、华为、联想、海尔、桔子、橙子等
            - 类别澄清：水果、粮食、食品、零食、饮料、手机、电脑、路由器、打印机、家电等
            - 必须检查正向、反向、默认三种消歧情况

            **2. 购买意图表达提取**（在无歧义消歧情况下优先执行）：

            **购买意图模式识别**：
            - "我想买X" → 提取"X"
            - "我要买X" → 提取"X"
            - "帮我找X" → 提取"X"
            - "搜索X" → 提取"X"
            - "看看X" → 提取"X"
            - "需要X" → 提取"X"
            - "买个X" → 提取"X"
            - "买点X" → 提取"X"

            **提取规则**：
            - 识别购买意图关键词：我想买、我要买、帮我找、搜索、看看、需要、买个、买点等
            - 提取关键词后面的商品名称作为核心查询词
            - 如果提取的商品名称是完整的商品词汇，直接使用
            - 去除购买意图前缀，保留核心商品信息

            **购买意图提取示例**：
            - "我想买水果" → **改写为"水果"**
            - "我要买手机" → **改写为"手机"**
            - "帮我找运动鞋" → **改写为"运动鞋"**
            - "搜索笔记本电脑" → **改写为"笔记本电脑"**
            - "看看苹果手机" → **改写为"苹果手机"**
            - "需要一个背包" → **改写为"背包"**
            - "买个充电器" → **改写为"充电器"**
            - "买点零食" → **改写为"零食"**

            **隐含购买需求识别**：
            - "我的X丢了" → 提取"X"
            - "我的X坏了" → 提取"X"
            - "X没有了" → 提取"X"
            - "X用完了" → 提取"X"
            - "缺少X" → 提取"X"
            - "没有X" → 提取"X"
            - "X不够用" → 提取"X"
            - "X不见了" → 提取"X"

            **隐含购买需求提取示例**：
            - "我的雨伞丢了" → **改写为"雨伞"**
            - "手机坏了" → **改写为"手机"**
            - "充电器没有了" → **改写为"充电器"**
            - "洗发水用完了" → **改写为"洗发水"**
            - "缺少一个背包" → **改写为"背包"**
            - "没有合适的鞋子" → **改写为"鞋子"**
            - "耳机不够用" → **改写为"耳机"**
            - "钥匙不见了" → **改写为"钥匙"**

            **3. 其他改写规则**（仅在无歧义消歧和购买意图提取都不适用时执行）：
            - 价格反馈处理
            - 属性补充
            - 智能合并

            **4. 最终检查**：
            - 确保所有歧义消歧情况都被正确处理
            - 确保购买意图表达被正确提取
            - 优先级：反向消歧 > 正向消歧 > 购买意图提取 > 默认处理 > 其他改写规则

            **如果无需改写**：仅当完全无关联且无歧义情况时，返回原查询

            请只返回改写后的查询，不要包含其他解释：
            """

            # 使用LLM进行query改写
            rewritten_query = ""
            async for chunk in self.chat.stream_chat_with_interrupt(rewrite_prompt, model=MODEL_NAME):
                rewritten_query += chunk

            # 清理改写结果
            rewritten_query = rewritten_query.strip().strip('"').strip("'")

            # 验证改写结果
            if not rewritten_query or len(rewritten_query) > 50:
                print(f"⚠️ 改写结果异常，使用原查询: {original_query}")
                return original_query

            # 如果改写结果与原查询相同或过于相似，返回原查询
            if rewritten_query.lower() == original_query.lower():
                print(f"✅ 改写结果与原查询相同，保持原查询: {original_query}")
                return original_query

            print(f"✅ 查询改写完成: '{original_query}' -> '{rewritten_query}'")
            return rewritten_query

        except Exception as e:
            print(f"❌ 查询改写出错: {e}")
            import traceback
            traceback.print_exc()
            # 出错时返回原查询
            return original_query

    def _get_last_search_product(self) -> str:
        """获取最近搜索的商品信息"""
        try:
            # 从对话历史中找到最近的搜索意图
            for turn in reversed(self.conversation_memory.conversation_history):
                if turn.intent == 'search':
                    # 优先返回改写后的查询，如果没有则返回原始输入
                    if turn.rewritten_query and turn.rewritten_query != turn.user_input:
                        return turn.rewritten_query
                    else:
                        return turn.user_input

            # 如果没有找到搜索历史，检查当前缓存的查询
            if hasattr(self, 'last_search_query') and self.last_search_query:
                return self.last_search_query

            return ""
        except Exception as e:
            print(f"❌ 获取最近搜索商品时出错: {e}")
            return ""

    def analyze_search_results_info(self, search_results: list, query: str):
        """分析搜索结果的商品信息"""
        try:
            if not search_results:
                return

            print(f"📊 分析搜索结果商品信息，共{len(search_results)}个商品")

            # 提取价格信息
            prices = []
            products_info = []

            for product in search_results:
                try:
                    # 尝试从不同字段获取价格
                    price_str = product.get('itemPrice', '0')
                    if not price_str:
                        price_str = product.get('price', '0')

                    # 清理价格字符串，提取数字
                    import re
                    price_match = re.search(r'[\d.]+', str(price_str))
                    if price_match:
                        price = float(price_match.group())
                        prices.append(price)

                        # 保存商品基本信息
                        product_info = {
                            'title': product.get('itemTitle', ''),
                            'price': price,
                            'id': product.get('itemId', ''),
                            'seller': product.get('seller_name', '')
                        }
                        products_info.append(product_info)
                except Exception as e:
                    print(f"⚠️ 解析商品价格时出错: {e}")
                    continue

            if prices:
                # 计算价格统计信息
                min_price = min(prices)
                max_price = max(prices)
                avg_price = sum(prices) / len(prices)

                # 更新当前搜索结果信息
                from datetime import datetime
                self.current_search_results_info = {
                    'products': products_info,
                    'price_range': {'min': min_price, 'max': max_price},
                    'avg_price': avg_price,
                    'query': query,
                    'timestamp': datetime.now().isoformat(),
                    'total_count': len(search_results)
                }

                print(f"💰 价格分析结果:")
                print(f"   最低价: {min_price}元")
                print(f"   最高价: {max_price}元")
                print(f"   平均价: {avg_price:.2f}元")
                print(f"   商品数量: {len(products_info)}")

                # 价格分布分析
                price_ranges = {
                    'low': [p for p in prices if p <= avg_price * 0.7],
                    'medium': [p for p in prices if avg_price * 0.7 < p <= avg_price * 1.3],
                    'high': [p for p in prices if p > avg_price * 1.3]
                }

                print(f"   价格分布: 低价({len(price_ranges['low'])}个) 中价({len(price_ranges['medium'])}个) 高价({len(price_ranges['high'])}个)")

            else:
                print("⚠️ 未能提取到有效的价格信息")

        except Exception as e:
            print(f"❌ 分析搜索结果信息时出错: {e}")
            import traceback
            traceback.print_exc()

    def get_price_suggestion_based_on_results(self) -> dict:
        """基于当前搜索结果给出价格建议"""
        try:
            if not self.current_search_results_info.get('products'):
                return {}

            info = self.current_search_results_info
            min_price = info['price_range']['min']
            max_price = info['price_range']['max']
            avg_price = info['avg_price']

            # 生成价格建议
            suggestions = {
                'current_range': f"{min_price:.0f}-{max_price:.0f}元",
                'avg_price': f"{avg_price:.0f}元",
                'lower_suggestions': [],
                'has_cheaper_options': False
            }

            # 如果最低价还比较高，建议更低的价格范围
            if min_price > 100:
                suggestions['lower_suggestions'].append(f"{min_price * 0.7:.0f}元以下")
                suggestions['has_cheaper_options'] = True
            if min_price > 50:
                suggestions['lower_suggestions'].append(f"{min_price * 0.5:.0f}元以下")
                suggestions['has_cheaper_options'] = True

            return suggestions

        except Exception as e:
            print(f"❌ 生成价格建议时出错: {e}")
            return {}

    async def should_ask_price_preference(self, user_input: str) -> dict:
        """判断是否应该询问用户的价格偏好"""
        try:
            # 检查是否有搜索结果信息
            if not self.current_search_results_info.get('products'):
                return {'should_ask': False}

            # 检查用户输入是否表达了价格不满意但没有给出具体价位
            price_dissatisfaction_keywords = [
                '太贵', '太貴', '贵了', '貴了', '钱不够', '錢不夠',
                '便宜一点', '便宜一點', '便宜的', '实惠', '實惠',
                '这些都太贵', '有没有便宜', '有沒有便宜'
            ]

            # 检查是否包含具体价格
            import re
            has_specific_price = bool(re.search(r'\d+元|元以下|元以内|元左右', user_input))

            # 检查是否表达价格不满意
            has_price_dissatisfaction = any(keyword in user_input for keyword in price_dissatisfaction_keywords)

            if has_price_dissatisfaction and not has_specific_price:
                # 生成价格询问建议
                price_range = self.current_search_results_info.get('price_range', {})
                min_price = price_range.get('min', 0)
                avg_price = self.current_search_results_info.get('avg_price', 0)

                # 生成合理的价格选项
                price_options = []
                if min_price > 100:
                    price_options.append(f"{min_price * 0.7:.0f}元以下")
                if min_price > 50:
                    price_options.append(f"{min_price * 0.5:.0f}元以下")
                if avg_price > 100:
                    price_options.append(f"{avg_price * 0.6:.0f}元以下")

                return {
                    'should_ask': True,
                    'current_range': f"{min_price:.0f}-{price_range.get('max', 0):.0f}元",
                    'suggested_options': price_options[:2],  # 最多2个选项
                    'query': self.current_search_results_info.get('query', '')
                }

            return {'should_ask': False}

        except Exception as e:
            print(f"❌ 判断价格询问时出错: {e}")
            return {'should_ask': False}

    async def generate_price_inquiry(self, price_info: dict) -> str:
        """生成价格询问问题"""
        try:
            current_range = price_info.get('current_range', '')
            suggested_options = price_info.get('suggested_options', [])
            query = price_info.get('query', '商品')

            if suggested_options:
                options_text = "、".join(suggested_options)
                question = f"我理解您觉得当前{query}的价格偏高（{current_range}）。您希望价格在什么范围内呢？比如{options_text}？"
            else:
                question = f"我理解您觉得当前{query}的价格偏高（{current_range}）。请问您的预算大概是多少呢？"

            return question

        except Exception as e:
            print(f"❌ 生成价格询问时出错: {e}")
            return "请问您希望价格在什么范围内呢？"

    def print_conversation_memory_status(self):
        """打印当前对话记忆状态（用于调试）"""
        try:
            print(f"\n📝 对话记忆状态 (Session: {self.conversation_memory.session_id[:8]}...)")
            print(f"   对话轮次数: {len(self.conversation_memory.conversation_history)}")

            if self.conversation_memory.conversation_history:
                print("   最近3轮对话:")
                for i, turn in enumerate(self.conversation_memory.conversation_history[-3:]):
                    print(f"   {i+1}. [{turn.intent}] {turn.user_input}")
                    if turn.rewritten_query and turn.rewritten_query != turn.user_input:
                        print(f"      改写为: {turn.rewritten_query}")

            if self.conversation_memory.current_search_context:
                print(f"   搜索上下文: {self.conversation_memory.current_search_context}")

            print("   " + "="*50)
        except Exception as e:
            print(f"❌ 打印对话记忆状态出错: {e}")

    async def generate_clarification_question(self, transcript: str, intent_result: dict):
        """使用LLM生成合适的澄清问题"""
        try:
            clarification_prompt = f"""
            用户说了："{transcript}"

            根据意图分析，这个输入比较模糊，需要进一步澄清。
            分析结果：{intent_result.get('reason', '意图不明确')}

            作为一个语音购物助手，请生成一个友好、自然的澄清问题来帮助理解用户的真实需求。

            澄清问题应该：
            1. 语气友好、自然，不要生硬
            2. 简洁明了，适合语音交互（不超过30字）
            3. 能够引导用户提供更具体的信息
            4. 考虑购物场景，但不要强制引导到购物

            示例风格：
            - 如果用户说了模糊词汇 → "您想要找什么类型的商品呢？"
            - 如果用户意图不清 → "您是想要搜索商品，还是有其他需要帮助的？"
            - 如果用户表达不完整 → "能告诉我更具体一些吗？"

            请生成一个合适的澄清问题：
            """

            # 使用LLM生成澄清问题
            clarification_response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(clarification_prompt, model=MODEL_NAME):
                clarification_response += chunk

            # 清理回复，去除多余的引号和格式
            clarification_response = clarification_response.strip().strip('"').strip("'")

            # 如果LLM回复为空或过长，使用默认问题
            if not clarification_response or len(clarification_response) > 50:
                clarification_response = "您想要搜索什么商品呢？或者有什么其他需要帮助的吗？"

            return clarification_response

        except Exception as e:
            print(f"❌ 生成澄清问题时出错: {e}")
            return "您想要搜索什么商品呢？或者有什么其他需要帮助的吗？"

    async def handle_interaction_intent(self, transcript: str, conv_id: str, intent_result: dict):
        """处理交互意图（闲聊、天气、百科等）- 已弃用，保留用于兼容性"""
        try:
            print(f"💬 处理交互意图: {transcript}")

            # 记录交互意图到memory（交互意图不进行query改写）
            turn = ConversationTurn(
                turn_id=conv_id,
                user_input=transcript,
                intent='interaction',
                confidence=intent_result.get('confidence', 0.8)
            )
            self.conversation_memory.add_turn(turn)

            # 判断是否需要web搜索
            needs_web_search = self.should_use_web_search(transcript)

            if needs_web_search:
                print(f"🌐 检测到需要web搜索的查询: {transcript}")
                await self.handle_web_search_interaction(transcript, conv_id)
            else:
                print(f"💭 处理普通交互意图: {transcript}")
                await self.handle_simple_interaction(transcript, conv_id)

            print(f"✅ 交互意图处理完成")

        except Exception as e:
            print(f"❌ 交互意图处理出错: {e}")
            import traceback
            traceback.print_exc()
            # 发送错误回复
            error_event = ChatEvent()
            error_event.content = "抱歉，我刚才走神了，您能再说一遍吗？"
            error_event.id = conv_id + "_interaction_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(error_event.content)

    def should_use_web_search(self, transcript: str) -> bool:
        """判断是否需要使用web搜索"""
        try:
            # 清理输入
            text = transcript.strip().lower()

            # 需要web搜索的关键词模式
            web_search_patterns = [
                # 天气相关（包含繁体字）
                '天气', '天氣', '温度', '溫度', '下雨', '下雨', '晴天', '阴天', '陰天',
                '雪', '风', '風', '气温', '氣溫', '湿度', '濕度',
                '会下雨吗', '會下雨嗎', '今天天气', '今天天氣', '明天天气', '明天天氣',
                '天气怎么样', '天氣怎麼樣', '气候', '氣候',

                # 时间和日期相关（包含繁体字）
                '现在几点', '現在幾點', '今天几号', '今天幾號', '星期几', '星期幾',
                '什么时候', '什麼時候', '时间', '時間', '日期', '日期',
                '今天是', '明天是', '昨天是', '现在是', '現在是',

                # 百科知识相关（包含繁体字）
                '什么是', '什麼是', '怎么样', '怎麼樣', '为什么', '為什麼',
                '如何', '怎么', '怎麼', '介绍一下', '介紹一下',
                '历史', '歷史', '文化', '科学', '科學', '技术', '技術',
                '地理', '人物', '事件', '知识', '知識', '资料', '資料',
                '信息', '信息', '背景', '原理', '定义', '定義',

                # 新闻和时事相关（包含繁体字）
                '新闻', '新聞', '最新', '最近', '现在', '現在', '当前', '當前',
                '今年', '今天发生', '今天發生', '最新消息', '最新消息',
                '最新情况', '最新情況', '现状', '現狀', '趋势', '趨勢', '发展', '發展',

                # 比较和对比相关（包含繁体字）
                '比较', '比較', '对比', '對比', '哪个好', '哪個好',
                '区别', '區別', '差异', '差異', '优缺点', '優缺點',
                '选择', '選擇', '推荐', '推薦', '建议', '建議', '评价', '評價', '评测', '評測',

                # 旅游和地点相关（包含繁体字）
                '旅游', '旅遊', '景点', '景點', '攻略', '路线', '路線',
                '交通', '住宿', '好玩', '值得去', '怎么去', '怎麼去',
                '在哪里', '在哪裡', '地址', '地址',

                # 健康和生活相关（包含繁体字）
                '健康', '养生', '養生', '营养', '營養', '锻炼', '鍛煉',
                '饮食', '飲食', '睡眠', '怎么做', '怎麼做', '方法',
                '技巧', '注意事项', '注意事項',

                # 学习和教育相关（包含繁体字）
                '学习', '學習', '教育', '课程', '課程', '考试', '考試',
                '培训', '培訓', '技能', '怎么学', '怎麼學', '如何提高',
                '方法', '经验', '經驗'
            ]

            # 检查是否包含需要web搜索的关键词
            for pattern in web_search_patterns:
                if pattern in text:
                    print(f"🔍 检测到web搜索关键词: {pattern}")
                    return True

            # 检查是否是问句形式（通常需要搜索回答）- 包含繁体字
            question_patterns = [
                '什么', '什麼', '怎么', '怎麼', '如何', '为什么', '為什麼',
                '哪里', '哪裡', '哪个', '哪個', '多少', '几', '幾',
                '吗', '嗎', '呢', '呢'
            ]
            if any(pattern in text for pattern in question_patterns) and len(text) > 3:
                print(f"🔍 检测到问句形式，可能需要web搜索")
                return True

            return False

        except Exception as e:
            print(f"❌ 判断web搜索需求时出错: {e}")
            return False

    async def handle_web_search_interaction(self, transcript: str, conv_id: str):
        """处理需要web搜索的交互意图"""
        try:
            print(f"🌐 开始web搜索处理: {transcript}")

            # 立即发送搜索状态消息，充分利用等待时间
            search_status = ChatEvent()
            search_status.content = f"正在为您查找相关信息..."
            search_status.id = conv_id + "_web_search_status"
            search_status.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(search_status))
            await self.voice_inqueue.put(search_status.content)

            # 并行为时效性查询添加当前时间
            enhanced_query = await self.enhance_query_with_time_context(transcript)
            print(f"🕒 增强后的查询: {enhanced_query}")

            # 并行调用web搜索，在状态消息播放的同时执行搜索
            try:
                search_results, _ = fetch_rag(enhanced_query)
                print(f"🌐 web搜索完成，获得{len(search_results) if search_results else 0}个结果")

                if search_results and len(search_results) > 0:
                    # 处理搜索结果，生成适合语音的回复
                    voice_response = await self.format_web_search_response(
                        transcript, search_results, enhanced_query
                    )
                else:
                    # 搜索无结果时的回复
                    voice_response = await self.handle_no_web_search_results(transcript)

            except Exception as search_error:
                print(f"❌ web搜索失败: {search_error}")
                # 搜索失败时回退到普通交互处理
                voice_response = await self.handle_web_search_fallback(transcript)

            # 发送回复
            async for chunk in self.stream_text_response(voice_response):
                te = ChatEvent()
                te.content = chunk
                te.id = conv_id + "_web_search_response"
                te.type = 'msg'
                await self.voice_queue.put(AdditionalOutputs(te))
                await self.voice_inqueue.put(chunk)

        except Exception as e:
            print(f"❌ web搜索交互处理出错: {e}")
            import traceback
            traceback.print_exc()
            # 回退到普通交互处理
            await self.handle_simple_interaction(transcript, conv_id)

    async def handle_simple_interaction(self, transcript: str, conv_id: str):
        """处理普通交互意图（不需要web搜索）"""
        try:
            print(f"💭 处理普通交互: {transcript}")

            # 立即发送处理状态消息，充分利用等待时间
            # 只在需要时发送处理状态消息
            if self.should_show_status_message("interaction_thinking"):
                status_event = ChatEvent()
                status_event.content = "让我想想..."
                status_event.id = conv_id + "_interaction_status"
                status_event.type = 'msg'
                await self.voice_queue.put(AdditionalOutputs(status_event))
                await self.voice_inqueue.put(status_event.content)

            # 并行构建交互意图的LLM提示并处理
            interaction_prompt = f"""
            用户说："{transcript}"

            这是一个语音助手的对话。用户的输入被识别为交互意图（闲聊、问候、感谢等）。

            请你：
            1. 用温和、友好的语气回应用户
            2. 提供情绪价值，让用户感到被关心
            3. 专注于回答用户的问题或回应用户的话题
            4. 回复要简洁，适合语音交互（不超过60字）
            5. 保持对话的自然性，不要强行引导到购物话题
            6. 只有在用户明确表达购物需求或询问商品时才提及购物相关内容

            回复原则：
            - 问候类：简单友好地回应，询问如何帮助
            - 感谢类：礼貌回应，表达乐意帮助的态度
            - 闲聊类：自然对话，保持轻松氛围
            - 情感类：给予温暖的回应和支持

            示例风格：
            - 用户说"你好" → "您好！很高兴为您服务，有什么可以帮您的吗？"
            - 用户说"谢谢" → "不客气！随时为您服务。"
            - 用户说"你真棒" → "谢谢您的夸奖！我会继续努力为您提供更好的服务。"

            请生成一个自然、温暖的回复：
            """

            # 在状态消息播放的同时，并行使用LLM生成交互回复
            async for chunk in self.chat.stream_chat_with_interrupt(interaction_prompt, model=MODEL_NAME):
                te = ChatEvent()
                te.content = chunk
                te.id = conv_id + "_simple_interaction"
                te.type = 'msg'
                await self.voice_queue.put(AdditionalOutputs(te))
                await self.voice_inqueue.put(chunk)

        except Exception as e:
            print(f"❌ 普通交互处理出错: {e}")
            # 发送默认回复
            default_response = "很高兴和您聊天！有什么可以帮您的吗？"
            error_event = ChatEvent()
            error_event.content = default_response
            error_event.id = conv_id + "_simple_interaction_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(default_response)

    async def enhance_query_with_time_context(self, query: str) -> str:
        """为时效性查询添加当前时间上下文"""
        try:
            from datetime import datetime

            # 清理输入
            text = query.strip().lower()

            # 需要时间上下文的关键词
            time_sensitive_keywords = [
                '今天', '现在', '当前', '最新', '最近', '今年', '这个月',
                '天气', '新闻', '股价', '汇率', '时间', '几点',
                '现任', '最新款', '最新版', '当季', '时令'
            ]

            # 检查是否包含时效性关键词
            needs_time_context = any(keyword in text for keyword in time_sensitive_keywords)

            if needs_time_context:
                # 获取当前时间
                now = datetime.now()
                current_time = now.strftime("%Y年%m月%d日 %H:%M")
                current_date = now.strftime("%Y年%m月%d日")

                # 根据查询类型添加不同的时间上下文
                if any(keyword in text for keyword in ['天气', '温度', '下雨', '晴天']):
                    enhanced_query = f"{query} {current_date}"
                elif any(keyword in text for keyword in ['新闻', '最新', '最近', '现在']):
                    enhanced_query = f"{query} {current_date}"
                elif any(keyword in text for keyword in ['现在几点', '时间']):
                    enhanced_query = f"{query} {current_time}"
                else:
                    enhanced_query = f"{query} {current_date}"

                print(f"🕒 添加时间上下文: '{query}' -> '{enhanced_query}'")
                return enhanced_query
            else:
                return query

        except Exception as e:
            print(f"❌ 添加时间上下文时出错: {e}")
            return query

    async def format_web_search_response(self, original_query: str, search_results: list, _: str) -> str:
        """格式化web搜索结果为适合语音的回复"""
        try:
            print(f"📝 格式化web搜索结果，共{len(search_results)}个结果")

            # 提取关键信息
            key_info = []
            for result in search_results[:3]:  # 只使用前3个结果
                if isinstance(result, dict):
                    title = result.get('title', '')
                    content = result.get('content', '')

                    # 清理和截取内容
                    if content:
                        # 移除多余的空白和换行
                        content = ' '.join(content.split())
                        # 截取前200字符
                        if len(content) > 200:
                            content = content[:200] + "..."
                        key_info.append(f"{title}: {content}")
                    elif title:
                        key_info.append(title)

            # 合并信息
            combined_info = " | ".join(key_info)

            # 使用LLM生成简洁的语音回复
            format_prompt = f"""
            用户询问："{original_query}"

            搜索到的信息：
            {combined_info}

            请基于搜索到的信息，生成一个简洁、准确的语音回复。要求：
            1. 直接回答用户的问题，不要重复问题
            2. 信息要准确，基于搜索结果
            3. 语言要自然、口语化，适合语音交互
            4. 控制在80字以内，重点突出
            5. 如果信息有时效性，要提及时间
            6. 避免过于详细的格式化文本

            示例风格：
            - 天气查询 → "今天北京多云，气温15-25度，适合出行。"
            - 百科查询 → "人工智能是模拟人类智能的技术，主要包括机器学习、深度学习等。"
            - 时间查询 → "现在是下午3点30分。"

            请生成简洁的语音回复：
            """

            # 使用LLM生成回复
            response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(format_prompt, model=MODEL_NAME):
                response += chunk

            # 清理回复
            response = response.strip()
            if len(response) > 150:
                response = response[:150] + "..."

            print(f"✅ 格式化完成: {response}")
            return response

        except Exception as e:
            print(f"❌ 格式化web搜索结果时出错: {e}")
            return f"根据搜索结果，关于「{original_query}」的信息已为您找到，但格式化时出现问题。"

    async def handle_no_web_search_results(self, query: str) -> str:
        """处理web搜索无结果的情况"""
        try:
            no_result_responses = [
                f"抱歉，没有找到关于「{query}」的相关信息。",
                f"很抱歉，暂时无法获取「{query}」的信息，请稍后再试。",
                f"关于「{query}」的信息暂时查找不到，您可以换个方式问问。"
            ]

            import random
            return random.choice(no_result_responses)

        except Exception as e:
            print(f"❌ 处理无搜索结果时出错: {e}")
            return f"抱歉，没有找到关于「{query}」的相关信息。"

    async def handle_web_search_fallback(self, query: str) -> str:
        """web搜索失败时的回退处理"""
        try:
            fallback_responses = [
                f"抱歉，查找「{query}」的信息时遇到了问题，请稍后再试。",
                f"网络搜索暂时不可用，关于「{query}」的问题我暂时无法回答。",
                f"很抱歉，无法为您搜索「{query}」的信息，请检查网络连接后重试。"
            ]

            import random
            return random.choice(fallback_responses)

        except Exception as e:
            print(f"❌ 处理web搜索回退时出错: {e}")
            return f"抱歉，查找「{query}」的信息时遇到了问题。"

    async def stream_text_response(self, text: str):
        """将文本转换为流式响应"""
        try:
            # 简单的流式输出，可以根据需要调整
            yield text
        except Exception as e:
            print(f"❌ 流式文本响应出错: {e}")
            yield text

    async def handle_search_intent(self, transcript: str, conv_id: str):
        """处理搜索意图 - 已弃用，保留用于兼容性"""
        try:
            print(f"🔍 处理搜索意图: {transcript}")

            # 1. 立即输出状态消息，充分利用等待时间
            # 先发送初始状态消息，让用户知道系统正在处理
            initial_status = ChatEvent()
            initial_status.content = f"正在为您搜索「{transcript}」相关商品..."
            initial_status.id = conv_id + "_initial_status"
            initial_status.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(initial_status))
            await self.voice_inqueue.put(initial_status.content)

            # 2. 并行执行query改写（仅对搜索意图）
            rewritten_query = await self.rewrite_search_query(transcript, conv_id)

            # 3. 记录对话轮次到memory
            turn = ConversationTurn(
                turn_id=conv_id,
                user_input=transcript,
                intent='search',
                confidence=0.9,
                search_query=transcript,
                rewritten_query=rewritten_query
            )
            self.conversation_memory.add_turn(turn)

            # 4. 如果query被改写，立即播报改写后的query
            if rewritten_query != transcript:
                rewrite_announcement = f"我理解您要搜索「{rewritten_query}」，正在为您查找相关商品..."
                rewrite_event = ChatEvent()
                rewrite_event.content = rewrite_announcement
                rewrite_event.id = conv_id + "_rewrite_announcement"
                rewrite_event.type = 'msg'
                await self.voice_queue.put(AdditionalOutputs(rewrite_event))
                await self.voice_inqueue.put(rewrite_announcement)

            # 5. 并行执行搜索 - 使用改写后的query
            # 在状态消息播放的同时执行搜索，充分利用等待时间
            try:
                search_result = self.search_tool.forward(rewritten_query)
                print(f"🔍 搜索原始结果类型: {type(search_result)}")
                print(f"🔍 搜索原始结果内容: {str(search_result)[:500]}...")

                if isinstance(search_result, list):
                    print(f"✅ 搜索完成，结果数量: {len(search_result)}")
                    if len(search_result) > 0:
                        print(f"🔍 第一个商品示例: {search_result[0]}")

                        # 使用商品详情工具增强搜索结果，获取图片等详细信息
                        # 只对前5个商品获取详情，但保留所有搜索结果
                        enhanced_first_batch = await self.enhance_with_details(search_result[:5])
                        # 将增强后的前5个商品与剩余商品合并
                        if len(enhanced_first_batch) == 5 and len(search_result) > 5:
                            search_result = enhanced_first_batch + search_result[5:]
                        else:
                            search_result = enhanced_first_batch
                        print(f"🛍️ 使用增强后的搜索结果，商品数量: {len(search_result)}")
                else:
                    print(f"⚠️ 搜索结果不是列表格式，使用模拟数据")
                    # 如果不是列表，尝试创建模拟数据用于测试
                    search_result = self.create_mock_search_results(transcript)
                    print(f"🔧 使用模拟数据: {len(search_result)} 个商品")

            except Exception as search_error:
                print(f"❌ 搜索API调用失败: {search_error}")
                import traceback
                traceback.print_exc()
                # 创建模拟数据用于测试
                search_result = self.create_mock_search_results(transcript)
                print(f"🔧 搜索失败，使用模拟数据: {len(search_result)} 个商品")

            # 格式化搜索结果（只显示第一批5个商品）
            first_batch = search_result[:self.batch_size] if len(search_result) > self.batch_size else search_result
            formatted_results = await self.format_search_results(first_batch, transcript)

            # 发送搜索结果到对话框
            result_event = SearchResultEvent()
            result_event.content = formatted_results
            result_event.id = conv_id + "_search_results"
            result_event.type = 'search_results'
            result_event.raw_data = first_batch
            await self.voice_queue.put(AdditionalOutputs(result_event))

            # 生成语音回复（基于当前批次）
            voice_summary = await self.generate_voice_summary(first_batch, transcript)

            # 通过LLM生成更自然的回复
            total_count = len(search_result)
            current_batch_count = len(first_batch)
            batch_info = f"共找到{total_count}个商品，当前显示前{current_batch_count}个" if total_count > current_batch_count else f"找到{total_count}个商品"

            llm_prompt = f"""
            用户搜索了「{transcript}」，我已经找到了相关商品。
            请用自然、友好的语气总结搜索结果，并询问用户是否需要了解更多详情。

            搜索结果摘要：{voice_summary}
            商品数量：{batch_info}

            请生成一个简洁的语音回复（不超过50字）。
            """

            async for chunk in self.chat.stream_chat_with_interrupt(llm_prompt, model=MODEL_NAME):
                te = ChatEvent()
                te.content = chunk
                te.id = conv_id
                te.type = 'msg'
                await self.voice_queue.put(AdditionalOutputs(te))
                await self.voice_inqueue.put(chunk)

            # 保存搜索结果用于后续的商品控制和批次管理
            if search_result and isinstance(search_result, list):
                # 分析搜索结果的商品信息（价格分布等）
                self.analyze_search_results_info(search_result, rewritten_query)

                # 保存所有搜索结果到缓存
                self.all_cached_products = search_result
                self.last_search_query = rewritten_query  # 使用改写后的查询

                # 计算批次信息
                self.total_batches = (len(search_result) + self.batch_size - 1) // self.batch_size
                self.current_batch_index = 0

                # 设置当前显示的商品（第一批）
                first_batch_end = min(self.batch_size, len(search_result))
                self.current_products = search_result[:first_batch_end]
                self.current_product_index = 0

                print(f"💾 已保存 {len(search_result)} 个商品用于语音控制")
                print(f"📦 批次信息: 总共{self.total_batches}批，当前显示第1批({len(self.current_products)}个商品)")

                # 更新对话轮次的agent_response
                if self.conversation_memory.conversation_history:
                    last_turn = self.conversation_memory.conversation_history[-1]
                    if last_turn.turn_id == conv_id:
                        last_turn.agent_response = f"找到{len(search_result)}个相关商品"

        except Exception as e:
            print(f"❌ 搜索处理出错: {e}")
            import traceback
            traceback.print_exc()
            error_event = ChatEvent()
            error_event.content = f"抱歉，搜索「{transcript}」时出现了问题，请稍后再试。"
            error_event.id = conv_id + "_error"
            error_event.type = 'msg'
            await self.voice_queue.put(AdditionalOutputs(error_event))
            await self.voice_inqueue.put(error_event.content)

    async def handle_product_control(self, transcript: str, conv_id: str):
        """处理商品控制指令（左滑、右滑、选择商品等）"""
        try:
            # 首先检查是否有可控制的商品 - 只有在有商品展示时才进行控制词匹配
            if not self.current_products or len(self.current_products) == 0:
                return False  # 没有商品时不进行控制词匹配，避免误识别

            # 清理和标准化输入
            command = transcript.strip().lower()
            # 移除常见的标点符号和换行符
            command = command.replace('\n', '').replace('\r', '').replace('。', '').replace('，', '').replace('、', '')

            # 定义控制指令映射，移除过于通用的单字词，只保留明确的控制指令
            control_commands = {
                # 左滑相关指令 - 移除单字"左"，保留明确指令
                '左滑': 'prev',
                '向左滑': 'prev',
                '左划': 'prev',
                '向左划': 'prev',
                '左华': 'prev',
                '向左华': 'prev',
                '左话': 'prev',
                '向左话': 'prev',
                '上一个': 'prev',
                '上一個': 'prev',
                '前一个': 'prev',
                '前一個': 'prev',
                '往左': 'prev',
                '向左': 'prev',
                '左边': 'prev',
                '左邊': 'prev',

                # 右滑相关指令 - 移除单字"右"，保留明确指令
                '右滑': 'next',
                '向右滑': 'next',
                '右划': 'next',
                '向右划': 'next',
                '右华': 'next',
                '向右华': 'next',
                '右话': 'next',
                '向右话': 'next',
                '向右晃': 'next',  # 常见ASR错误
                '右晃': 'next',
                '下一个': 'next',
                '下一個': 'next',
                '后一个': 'next',
                '后一個': 'next',
                '往右': 'next',
                '向右': 'next',
                '右边': 'next',
                '右邊': 'next',

                # 选择相关指令 - 移除过于通用的词，保留明确指令
                '选择': 'select',
                '選擇': 'select',
                '选择商品': 'select',
                '選擇商品': 'select',
                '选择这个': 'select',
                '選擇這個': 'select',
                '选择当前商品': 'select',
                '選擇當前商品': 'select',
                '确定': 'select',
                '確定': 'select',
                '要这个': 'select',
                '要這個': 'select',
                '就这个': 'select',
                '就這個': 'select',
                '买这个': 'select',
                '買這個': 'select',
                '选这个': 'select',
                '選這個': 'select',
                # 移除过于通用的"这个"、"好的"、"可以"、"行"

                # 换一批相关指令 - 移除过于通用的词
                '换一批': 'next_batch',
                '換一批': 'next_batch',
                '下一批': 'next_batch',
                '下一批商品': 'next_batch',
                '更多商品': 'next_batch',
                '换批': 'next_batch',
                '換批': 'next_batch',
                '下批': 'next_batch',
                '下一组': 'next_batch',
                '下一組': 'next_batch',
                '换组': 'next_batch',
                '換組': 'next_batch',
                '换一组': 'next_batch',
                '換一組': 'next_batch',
                '看更多': 'next_batch',
                '还有吗': 'next_batch',
                '還有嗎': 'next_batch',
                '还有其他的吗': 'next_batch',
                '還有其他的嗎': 'next_batch',
                '其他商品': 'next_batch',
                '换个': 'next_batch',
                '換個': 'next_batch',
                '换换': 'next_batch',
                '換換': 'next_batch',
                # 移除过于通用的"更多"、"其他的"、"别的"

                # 上一批相关指令
                '上一批': 'prev_batch',
                '上一批商品': 'prev_batch',
                '前一批': 'prev_batch',
                '前一批商品': 'prev_batch',
                '回到上一批': 'prev_batch',
                '返回上一批': 'prev_batch',
                '上一组': 'prev_batch',
                '上一組': 'prev_batch',
                '前一组': 'prev_batch',
                '前一組': 'prev_batch'
            }

            # 检查是否是控制指令 - 只使用精确匹配，移除过于宽泛的包含匹配
            action = None

            # 只进行精确匹配
            if command in control_commands:
                action = control_commands[command]
                print(f"🎯 精确匹配控制指令: '{command}' -> {action}")

            # 如果精确匹配失败，尝试严格的模糊匹配（处理ASR识别错误）
            if not action:
                action = self._fuzzy_match_control_command(command)

            if not action:
                return False  # 不是控制指令

            # 执行控制操作
            if action == 'prev':
                await self.handle_previous_product(conv_id)
            elif action == 'next':
                await self.handle_next_product(conv_id)
            elif action == 'select':
                await self.handle_select_product(conv_id)
            elif action == 'next_batch':
                await self.handle_next_batch(conv_id)
            elif action == 'prev_batch':
                await self.handle_prev_batch(conv_id)

            return True

        except Exception as e:
            print(f"❌ 商品控制处理出错: {e}")
            return False

    def _fuzzy_match_control_command(self, command: str):
        """模糊匹配控制指令，处理ASR识别错误 - 更严格的匹配"""
        try:
            # 定义更严格的关键词模式，避免误匹配
            patterns = {
                'prev': [
                    # 左滑相关模式 - 必须包含明确的方向和动作
                    ['左', '滑'], ['左', '划'], ['左', '华'], ['左', '话'],
                    ['向', '左', '滑'], ['向', '左', '划'], ['往', '左', '滑'],
                    ['上', '一', '个'], ['前', '一', '个'], ['上', '一', '個'], ['前', '一', '個']
                ],
                'next': [
                    # 右滑相关模式 - 必须包含明确的方向和动作
                    ['右', '滑'], ['右', '划'], ['右', '华'], ['右', '话'], ['右', '晃'],
                    ['向', '右', '滑'], ['向', '右', '划'], ['往', '右', '滑'],
                    ['下', '一', '个'], ['后', '一', '个'], ['下', '一', '個'], ['后', '一', '個']
                ],
                'select': [
                    # 选择相关模式 - 必须包含明确的选择意图
                    ['选', '择'], ['選', '擇'], ['确', '定'], ['確', '定'],
                    ['要', '这', '个'], ['要', '這', '個'], ['就', '这', '个'], ['就', '這', '個'],
                    ['买', '这', '个'], ['買', '這', '個'], ['选', '这', '个'], ['選', '這', '個']
                ],
                'next_batch': [
                    # 换一批相关模式 - 必须包含明确的批次切换意图
                    ['换', '一', '批'], ['換', '一', '批'], ['下', '一', '批'],
                    ['换', '批'], ['換', '批'], ['下', '批'],
                    ['换', '一', '组'], ['換', '一', '組'], ['下', '一', '组'], ['下', '一', '組'],
                    ['还', '有', '吗'], ['還', '有', '嗎'], ['还', '有', '其', '他'],
                    ['看', '更', '多'], ['更', '多', '商', '品']
                ],
                'prev_batch': [
                    # 上一批相关模式 - 必须包含明确的批次回退意图
                    ['上', '一', '批'], ['前', '一', '批'], ['上', '批'], ['前', '批'],
                    ['上', '一', '组'], ['上', '一', '組'], ['前', '一', '组'], ['前', '一', '組'],
                    ['回', '到', '上'], ['返', '回', '上']
                ]
            }

            # 检查每个模式 - 要求更严格的匹配
            for action, pattern_list in patterns.items():
                for pattern in pattern_list:
                    # 检查是否所有关键词都在命令中，且命令长度合理
                    if all(keyword in command for keyword in pattern):
                        # 额外检查：避免在长句子中误匹配
                        if len(command) <= 10 or len(pattern) >= 2:  # 短命令或多关键词模式
                            print(f"🔍 严格模糊匹配成功: '{command}' -> {action} (模式: {pattern})")
                            return action

            # 移除单字指令匹配，避免误识别
            # 不再支持单独的"左"、"右"等单字指令

            # 如果模式匹配失败，尝试相似度匹配（提高阈值）
            similarity_result = self._similarity_match_control_command(command)
            if similarity_result:
                return similarity_result

            return None

        except Exception as e:
            print(f"❌ 模糊匹配出错: {e}")
            return None

    def _similarity_match_control_command(self, command: str):
        """基于相似度的控制指令匹配 - 提高阈值避免误匹配"""
        try:
            # 定义标准指令 - 只保留明确的控制指令
            standard_commands = {
                '左滑': 'prev',
                '右滑': 'next',
                '向左滑': 'prev',
                '向右滑': 'next',
                '上一个': 'prev',
                '下一个': 'next',
                '选择': 'select',
                '选择商品': 'select',
                '确定': 'select',
                '换一批': 'next_batch',
                '上一批': 'prev_batch'
            }

            # 提高相似度阈值，避免误匹配
            similarity_threshold = 0.8  # 从0.6提高到0.8

            for std_cmd, action in standard_commands.items():
                similarity = self._calculate_similarity(command, std_cmd)
                if similarity >= similarity_threshold:
                    # 额外检查：命令长度不能相差太大
                    length_ratio = min(len(command), len(std_cmd)) / max(len(command), len(std_cmd))
                    if length_ratio >= 0.5:  # 长度比例不能相差太大
                        print(f"🔍 相似度匹配成功: '{command}' -> '{std_cmd}' -> {action} (相似度: {similarity:.2f}, 长度比例: {length_ratio:.2f})")
                        return action

            return None

        except Exception as e:
            print(f"❌ 相似度匹配出错: {e}")
            return None

    def _calculate_similarity(self, s1: str, s2: str):
        """计算两个字符串的相似度（基于编辑距离）"""
        try:
            if not s1 or not s2:
                return 0.0

            # 简化的编辑距离计算
            len1, len2 = len(s1), len(s2)
            if len1 == 0:
                return 0.0
            if len2 == 0:
                return 0.0

            # 创建距离矩阵
            dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]

            # 初始化
            for i in range(len1 + 1):
                dp[i][0] = i
            for j in range(len2 + 1):
                dp[0][j] = j

            # 计算编辑距离
            for i in range(1, len1 + 1):
                for j in range(1, len2 + 1):
                    if s1[i-1] == s2[j-1]:
                        dp[i][j] = dp[i-1][j-1]
                    else:
                        dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1

            # 计算相似度
            max_len = max(len1, len2)
            similarity = 1.0 - (dp[len1][len2] / max_len)
            return similarity

        except Exception as e:
            print(f"❌ 相似度计算出错: {e}")
            return 0.0

    async def handle_previous_product(self, conv_id: str):
        """处理左滑/上一个商品"""
        if self.current_product_index > 0:
            self.current_product_index -= 1
            current_product = self.current_products[self.current_product_index]
            title = current_product.get('itemTitle') or current_product.get('title', '商品')

            # 发送控制事件到前端
            control_event = SearchResultEvent()
            control_event.content = "product_control"
            control_event.id = conv_id + "_control"
            control_event.type = 'product_control'
            control_event.action = 'prev'
            control_event.product_index = self.current_product_index
            await self.voice_queue.put(AdditionalOutputs(control_event))

            response = f"已切换到上一个商品：{title}"
            await self.send_voice_response(response, conv_id)
            print(f"🔄 左滑到商品 {self.current_product_index + 1}/{len(self.current_products)}: {title}")
        else:
            response = "已经是第一个商品了。"
            await self.send_voice_response(response, conv_id)

    async def handle_next_product(self, conv_id: str):
        """处理右滑/下一个商品"""
        if self.current_product_index < len(self.current_products) - 1:
            self.current_product_index += 1
            current_product = self.current_products[self.current_product_index]
            title = current_product.get('itemTitle') or current_product.get('title', '商品')

            # 发送控制事件到前端
            control_event = SearchResultEvent()
            control_event.content = "product_control"
            control_event.id = conv_id + "_control"
            control_event.type = 'product_control'
            control_event.action = 'next'
            control_event.product_index = self.current_product_index
            await self.voice_queue.put(AdditionalOutputs(control_event))

            response = f"已切换到下一个商品：{title}"
            await self.send_voice_response(response, conv_id)
            print(f"🔄 右滑到商品 {self.current_product_index + 1}/{len(self.current_products)}: {title}")
        else:
            response = "已经是最后一个商品了。"
            await self.send_voice_response(response, conv_id)

    async def handle_select_product(self, conv_id: str):
        """处理选择商品"""
        if self.current_products and 0 <= self.current_product_index < len(self.current_products):
            current_product = self.current_products[self.current_product_index]
            title = current_product.get('itemTitle') or current_product.get('title', '商品')
            price = current_product.get('itemPrice') or current_product.get('price', '价格待询')

            # 发送选择事件到前端
            control_event = SearchResultEvent()
            control_event.content = "product_control"
            control_event.id = conv_id + "_control"
            control_event.type = 'product_control'
            control_event.action = 'select'
            control_event.product_index = self.current_product_index
            control_event.selected_product = current_product
            await self.voice_queue.put(AdditionalOutputs(control_event))

            response = f"您已选择商品：{title}，价格：{price}元。"
            await self.send_voice_response(response, conv_id)
            print(f"✅ 选择商品: {title} (价格: {price})")
        else:
            response = "没有可选择的商品。"
            await self.send_voice_response(response, conv_id)

    async def handle_next_batch(self, conv_id: str):
        """处理换一批商品"""
        try:
            # 检查是否有缓存的商品数据
            if not self.all_cached_products or len(self.all_cached_products) == 0:
                response = "请先搜索商品，然后再换一批。"
                await self.send_voice_response(response, conv_id)
                return

            # 计算下一批次的起始索引
            next_batch_start = (self.current_batch_index + 1) * self.batch_size

            # 检查是否还有更多商品
            if next_batch_start >= len(self.all_cached_products):
                response = f"已经是最后一批商品了，共{len(self.all_cached_products)}个商品。"
                await self.send_voice_response(response, conv_id)
                return

            # 切换到下一批
            self.current_batch_index += 1
            batch_end = min(next_batch_start + self.batch_size, len(self.all_cached_products))
            current_batch_products = self.all_cached_products[next_batch_start:batch_end]

            # 为当前批次获取详情（如果还没有的话）
            enhanced_batch = await self.enhance_batch_if_needed(current_batch_products)

            # 更新当前显示的商品列表
            self.current_products = enhanced_batch
            self.current_product_index = 0  # 重置商品索引到第一个

            print(f"🔄 换一批: 批次 {self.current_batch_index + 1}/{self.total_batches}, 商品数量: {len(enhanced_batch)}")

            # 格式化新批次的商品结果
            formatted_results = await self.format_search_results(enhanced_batch, self.last_search_query)

            # 发送批次更新事件到前端
            batch_event = SearchResultEvent()
            batch_event.content = formatted_results
            batch_event.id = conv_id + "_next_batch"
            batch_event.type = 'search_results'
            batch_event.raw_data = enhanced_batch
            batch_event.batch_info = {
                'current_batch': self.current_batch_index + 1,
                'total_batches': self.total_batches,
                'batch_size': len(enhanced_batch),
                'total_products': len(self.all_cached_products)
            }
            await self.voice_queue.put(AdditionalOutputs(batch_event))

            # 生成语音回复
            response = f"已为您换到第{self.current_batch_index + 1}批商品，共{len(enhanced_batch)}个商品。"
            await self.send_voice_response(response, conv_id)

        except Exception as e:
            print(f"❌ 换一批处理出错: {e}")
            import traceback
            traceback.print_exc()
            response = "换一批时出现了问题，请稍后再试。"
            await self.send_voice_response(response, conv_id)

    async def handle_prev_batch(self, conv_id: str):
        """处理上一批商品"""
        try:
            # 检查是否有缓存的商品数据
            if not self.all_cached_products or len(self.all_cached_products) == 0:
                response = "请先搜索商品，然后再切换批次。"
                await self.send_voice_response(response, conv_id)
                return

            # 检查是否已经是第一批
            if self.current_batch_index <= 0:
                response = "已经是第一批商品了。"
                await self.send_voice_response(response, conv_id)
                return

            # 切换到上一批
            self.current_batch_index -= 1
            batch_start = self.current_batch_index * self.batch_size
            batch_end = min(batch_start + self.batch_size, len(self.all_cached_products))
            current_batch_products = self.all_cached_products[batch_start:batch_end]

            # 为当前批次获取详情（如果还没有的话）
            enhanced_batch = await self.enhance_batch_if_needed(current_batch_products)

            # 更新当前显示的商品列表
            self.current_products = enhanced_batch
            self.current_product_index = 0  # 重置商品索引到第一个

            print(f"🔄 上一批: 批次 {self.current_batch_index + 1}/{self.total_batches}, 商品数量: {len(enhanced_batch)}")

            # 格式化新批次的商品结果
            formatted_results = await self.format_search_results(enhanced_batch, self.last_search_query)

            # 发送批次更新事件到前端
            batch_event = SearchResultEvent()
            batch_event.content = formatted_results
            batch_event.id = conv_id + "_prev_batch"
            batch_event.type = 'search_results'
            batch_event.raw_data = enhanced_batch
            batch_event.batch_info = {
                'current_batch': self.current_batch_index + 1,
                'total_batches': self.total_batches,
                'batch_size': len(enhanced_batch),
                'total_products': len(self.all_cached_products)
            }
            await self.voice_queue.put(AdditionalOutputs(batch_event))

            # 生成语音回复
            response = f"已为您切换到第{self.current_batch_index + 1}批商品，共{len(enhanced_batch)}个商品。"
            await self.send_voice_response(response, conv_id)

        except Exception as e:
            print(f"❌ 上一批处理出错: {e}")
            import traceback
            traceback.print_exc()
            response = "切换上一批时出现了问题，请稍后再试。"
            await self.send_voice_response(response, conv_id)

    async def send_voice_response(self, response: str, conv_id: str):
        """发送语音回复"""
        te = ChatEvent()
        te.content = response
        te.id = conv_id + "_control_response"
        te.type = 'msg'
        await self.voice_queue.put(AdditionalOutputs(te))
        await self.voice_inqueue.put(response)

    async def enhance_batch_if_needed(self, batch_products):
        """为批次商品获取详情（如果还没有的话）"""
        try:
            # 检查是否已经有详情信息（通过pic_url字段判断）
            needs_enhancement = False
            for product in batch_products:
                if not product.get('pic_url'):
                    needs_enhancement = True
                    break

            if needs_enhancement:
                print(f"🔍 为批次商品获取详情，商品数量: {len(batch_products)}")
                enhanced_batch = await self.enhance_with_details(batch_products)
                return enhanced_batch
            else:
                print(f"✅ 批次商品已有详情，跳过增强")
                return batch_products

        except Exception as e:
            print(f"❌ 批次增强出错: {e}")
            return batch_products

    async def enhance_with_details(self, search_results):
        """使用商品详情工具增强搜索结果，获取图片等详细信息"""
        try:
            if not search_results or not isinstance(search_results, list):
                return search_results

            # 提取商品ID列表（只处理传入的商品）
            item_ids = []
            for item in search_results:  # 处理传入的商品（通常是前5个）
                item_id = item.get('itemId') or item.get('nid') or item.get('id')
                if item_id:
                    item_ids.append(str(item_id))

            if not item_ids:
                print("⚠️ 未找到有效的商品ID，跳过详情获取")
                return search_results

            print(f"🔍 准备获取商品详情，ID列表: {item_ids}")

            # 调用商品详情工具
            import json
            item_ids_json = json.dumps(item_ids)
            # 传递搜索关键词给详情工具，用于更好的匹配
            query = getattr(self, 'current_query', '')
            details_result = self.detail_tool.forward(item_ids_json, query)

            # 解析详情结果
            if isinstance(details_result, str):
                try:
                    details_data = json.loads(details_result)
                except:
                    print("⚠️ 商品详情解析失败，使用原始搜索结果")
                    return search_results
            else:
                details_data = details_result

            if not isinstance(details_data, list):
                print("⚠️ 商品详情格式异常，使用原始搜索结果")
                return search_results

            print(f"✅ 获取到商品详情数量: {len(details_data)}")

            # 调试：打印详情数据结构
            if isinstance(details_data, list) and len(details_data) > 0:
                print(f"🔍 详情数据第一项的键: {list(details_data[0].keys()) if isinstance(details_data[0], dict) else 'N/A'}")
                print(f"🔍 详情数据第一项示例: {str(details_data[0])[:200]}...")

            # 将详情信息合并到搜索结果中
            enhanced_results = []
            for i, search_item in enumerate(search_results):  # 处理传入的搜索结果
                enhanced_item = search_item.copy()

                # 查找对应的详情信息
                search_id = str(search_item.get('itemId') or search_item.get('nid') or search_item.get('id') or '')
                detail_item = None

                print(f"🔍 正在查找商品ID: {search_id}")

                for j, detail in enumerate(details_data):
                    # 尝试多种可能的ID字段名
                    detail_id = str(detail.get('itemId') or detail.get('nid') or detail.get('id') or
                                  detail.get('item_id') or detail.get('nidlong') or '')
                    print(f"  - 详情项{j} ID: {detail_id}")
                    if detail_id == search_id:
                        detail_item = detail
                        break

                if detail_item:
                    # 合并详情信息，优先使用详情中的信息
                    # 处理图片URL - 尝试多种可能的字段名
                    pic_url = None

                    # 优先从imageInfo数组中获取图片URL
                    image_info = detail_item.get('imageInfo')
                    if isinstance(image_info, list) and len(image_info) > 0:
                        pic_url = image_info[0].get('imageUrl')

                    # 如果imageInfo中没有，尝试其他字段
                    if not pic_url:
                        pic_url = (detail_item.get('pic_path') or detail_item.get('pic_url') or
                                  detail_item.get('image') or enhanced_item.get('pic_url'))

                    # 如果图片URL不是完整URL，添加协议前缀
                    if pic_url and not pic_url.startswith('http'):
                        if pic_url.startswith('//'):
                            pic_url = 'https:' + pic_url
                        else:
                            pic_url = 'https://' + pic_url

                    # 缓存图片到本地
                    if pic_url:
                        cached_url = await self.cache_image(pic_url)
                        if cached_url and cached_url != pic_url:  # 只有成功缓存才使用本地URL
                            pic_url = cached_url

                    # 处理店铺信息
                    shop_info = detail_item.get('shopInfo', {})
                    shop_name = None
                    if isinstance(shop_info, dict):
                        shop_info_list = shop_info.get('shopInfoList', [])
                        if isinstance(shop_info_list, list) and len(shop_info_list) > 0:
                            shop_name = shop_info_list[0]

                    enhanced_item.update({
                        'pic_url': pic_url,
                        'itemTitle': detail_item.get('title') or enhanced_item.get('itemTitle') or enhanced_item.get('title'),
                        'itemPrice': detail_item.get('price') or enhanced_item.get('itemPrice') or enhanced_item.get('price'),
                        'seller_name': shop_name or enhanced_item.get('seller_name') or enhanced_item.get('shop_name'),
                        'transNum30d': detail_item.get('realSales') or enhanced_item.get('transNum30d') or enhanced_item.get('sales'),
                        'bcType': enhanced_item.get('bcType'),  # 保持原有的bcType
                        'itemId': detail_item.get('item_id') or detail_item.get('nidlong') or enhanced_item.get('itemId')
                    })
                    print(f"🖼️ 商品 {search_id} 详情合并完成，图片: {enhanced_item.get('pic_url', '无')}")
                    print(f"🏪 店铺信息: {enhanced_item.get('seller_name', '无')}")
                else:
                    print(f"⚠️ 未找到商品 {search_id} 的详情信息")

                enhanced_results.append(enhanced_item)

            return enhanced_results

        except Exception as e:
            print(f"❌ 获取商品详情时出错: {e}")
            import traceback
            traceback.print_exc()
            return search_results

    async def cache_image(self, image_url):
        """缓存图片到本地目录"""
        try:
            # 创建缓存目录
            cache_dir = Path(__file__).parent / "image_cache"
            cache_dir.mkdir(exist_ok=True)

            # 生成文件名（使用URL的hash值）
            url_hash = hashlib.md5(image_url.encode()).hexdigest()

            # 尝试多种可能的扩展名
            possible_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

            # 首先检查是否已经有缓存文件
            for ext in possible_extensions:
                cached_file = cache_dir / f"{url_hash}{ext}"
                if cached_file.exists():
                    print(f"✅ 使用已缓存图片: {cached_file.name}")
                    return f"/image_cache/{cached_file.name}"

            # 如果没有缓存，尝试下载
            file_extension = '.jpg'  # 默认使用jpg扩展名

            # 尝试从URL中提取扩展名
            if '.' in image_url.split('/')[-1]:
                original_ext = '.' + image_url.split('.')[-1].split('?')[0].lower()
                if original_ext in possible_extensions:
                    file_extension = original_ext

            cached_file = cache_dir / f"{url_hash}{file_extension}"

            print(f"📥 正在缓存图片: {image_url}")

            # 使用urllib下载图片，避免代理问题
            try:
                # 创建SSL上下文，忽略证书验证
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                # 创建请求，模拟真实浏览器
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Referer': 'https://www.taobao.com/',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'cross-site',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
                req = urllib.request.Request(image_url, headers=headers)

                # 下载图片
                with urllib.request.urlopen(req, context=ssl_context, timeout=5) as response:
                    image_data = response.read()

                # 保存到本地
                with open(cached_file, 'wb') as f:
                    f.write(image_data)

                print(f"✅ 图片缓存成功: {cached_file.name}")
                return f"/image_cache/{cached_file.name}"

            except Exception as e:
                print(f"❌ urllib下载失败，尝试requests方法: {e}")

                # 备选方案：使用requests库
                try:
                    import requests
                    session = requests.Session()
                    session.headers.update({
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Referer': 'https://www.taobao.com/',
                        'Sec-Fetch-Dest': 'image',
                        'Sec-Fetch-Mode': 'no-cors',
                        'Sec-Fetch-Site': 'cross-site'
                    })

                    # 禁用代理
                    proxies = {'http': '', 'https': ''}

                    response = session.get(
                        image_url,
                        timeout=10,
                        verify=False,
                        proxies=proxies,
                        allow_redirects=True
                    )
                    response.raise_for_status()

                    # 保存到本地
                    with open(cached_file, 'wb') as f:
                        f.write(response.content)

                    print(f"✅ requests下载成功: {cached_file.name}")
                    return f"/image_cache/{cached_file.name}"

                except Exception as e2:
                    print(f"❌ requests下载也失败，使用备选缓存图片: {e2}")

                    # 如果所有下载方法都失败，尝试使用任意一个已有的缓存图片作为备选
                    cached_files = list(cache_dir.glob("*.jpg")) + list(cache_dir.glob("*.png"))
                    if cached_files:
                        fallback_file = cached_files[0]  # 使用第一个找到的缓存图片
                        print(f"🔄 使用备选缓存图片: {fallback_file.name}")
                        return f"/image_cache/{fallback_file.name}"
                    else:
                        print(f"❌ 没有可用的备选图片，使用原始URL")
                        return image_url  # 返回原始URL作为最后备选

        except Exception as e:
            print(f"❌ 图片缓存处理失败 {image_url}: {e}")
            return image_url  # 返回原始URL作为备选

    def create_mock_search_results(self, query):
        """创建模拟搜索结果用于测试"""
        # 生成更多模拟商品用于测试换一批功能
        mock_results = []

        # 商品类型和店铺名称
        product_types = ['高品质', '热销', '精选', '爆款', '新品', '特价', '限量', '经典', '推荐', '优选',
                        '畅销', '人气', '口碑', '实惠', '超值', '品牌', '正品', '原装', '进口', '专业']
        shop_types = ['官方旗舰店', '品牌专营店', '优质商家', '金牌卖家', '皇冠店铺', '信誉商家',
                     '专业店铺', '认证商家', '五星店铺', '推荐商家', '热销店铺', '口碑店铺']

        # 生成15个商品（可以分成3批，每批5个）
        for i in range(15):
            product_type = product_types[i % len(product_types)]
            shop_name = shop_types[i % len(shop_types)]
            price = 99 + i * 50  # 价格递增
            sales = 100 + i * 200  # 销量递增

            mock_results.append({
                'title': f'{query} - {product_type}商品{i+1}',
                'itemTitle': f'{query} - {product_type}商品{i+1}',
                'price': f'{price}.00',
                'itemPrice': f'{price}.00',
                'pic_url': f'https://img.alicdn.com/imgextra/i{(i%3)+1}/6000000000000/O1CN01example{i+1}.jpg',
                'nid': f'{123456789 + i}',
                'itemId': f'{123456789 + i}',
                'shop_name': shop_name,
                'seller_name': shop_name,
                'sales': f'月销{sales}+',
                'transNum30d': f'{sales}',
                'bcType': 'taobao' if i % 2 == 0 else 'tmall'
            })

        print(f"🔧 生成了 {len(mock_results)} 个模拟商品用于测试")
        return mock_results

    async def format_search_results(self, search_result, query):
        """格式化搜索结果为HTML展示"""
        try:
            if not search_result or not isinstance(search_result, list):
                return f"<div class='search-no-results'>未找到「{query}」相关商品</div>"

            html_parts = [f"<div class='search-results-container' style='max-width: 100%; padding: 10px;'>"]

            # 添加批次信息显示
            batch_info = ""
            if hasattr(self, 'total_batches') and self.total_batches > 1:
                batch_info = f" (第{self.current_batch_index + 1}批/共{self.total_batches}批)"

            html_parts.append(f"<h3 style='color: #1890ff; margin-bottom: 15px;'>🛍️ 「{query}」搜索结果{batch_info} ({len(search_result)}个商品)</h3>")

            # 如果有多批次，添加批次导航提示
            if hasattr(self, 'total_batches') and self.total_batches > 1:
                remaining_products = len(self.all_cached_products) - (self.current_batch_index + 1) * self.batch_size
                if remaining_products > 0:
                    html_parts.append(f"<p style='color: #52c41a; font-size: 14px; margin-bottom: 10px;'>💡 还有{remaining_products}个商品，说\"换一批\"查看更多</p>")

            for i, item in enumerate(search_result[:5]):  # 只显示前5个结果
                try:
                    # 适配实际API返回的字段名
                    title = item.get('itemTitle', item.get('title', '商品标题'))
                    price = item.get('itemPrice', item.get('price', '价格待询'))
                    image = item.get('pic_url', '')  # 这个字段可能不存在
                    item_id = item.get('itemId', item.get('nid', ''))
                    shop_name = item.get('seller_name', item.get('shop_name', ''))
                    sales = item.get('transNum30d', item.get('sales', ''))
                    bc_type = item.get('bcType', '')  # 平台类型

                    # 清理价格格式
                    if isinstance(price, (int, float)):
                        price = f"{price:.2f}"
                    elif isinstance(price, str):
                        price = price.replace('¥', '').strip()

                    html_parts.append(f"""
                    <div class='product-item' style='
                        border: 1px solid #e8e8e8;
                        margin: 10px 0;
                        padding: 15px;
                        border-radius: 8px;
                        background: #fafafa;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    '>
                        <div style='display: flex; align-items: flex-start; gap: 15px;'>
                            <div style='flex: 1;'>
                                <h4 style='
                                    margin: 0 0 8px 0;
                                    color: #333;
                                    font-size: 16px;
                                    line-height: 1.4;
                                    font-weight: 500;
                                '>{title}</h4>
                                <p style='
                                    margin: 0 0 5px 0;
                                    color: #ff4d4f;
                                    font-size: 20px;
                                    font-weight: bold;
                                '>¥{price}</p>
                                {f'<p style="margin: 0 0 5px 0; color: #666; font-size: 12px;">🏪 {shop_name}</p>' if shop_name else ''}
                                {f'<p style="margin: 0 0 5px 0; color: #52c41a; font-size: 12px;">📈 月销{sales}</p>' if sales else ''}
                                {f'<p style="margin: 0 0 5px 0; color: #1890ff; font-size: 12px;">🏷️ {bc_type}</p>' if bc_type else ''}
                                <p style='margin: 5px 0 0 0; color: #999; font-size: 11px;'>商品ID: {item_id}</p>
                            </div>
                            {f'''<img src="{image}" style="
                                width: 80px;
                                height: 80px;
                                object-fit: cover;
                                border-radius: 4px;
                                border: 1px solid #e8e8e8;
                            " onerror="this.style.display='none'" />''' if image else ''}
                        </div>
                    </div>
                    """)
                except Exception as e:
                    print(f"格式化商品项出错: {e}")
                    continue

            html_parts.append("</div>")
            formatted_html = "".join(html_parts)
            print(f"📄 格式化完成，HTML长度: {len(formatted_html)} 字符")
            return formatted_html

        except Exception as e:
            print(f"格式化搜索结果出错: {e}")
            import traceback
            traceback.print_exc()
            return f"<div class='search-error'>搜索结果格式化失败: {str(e)}</div>"

    async def generate_voice_summary(self, search_result, query):
        """生成语音摘要"""
        try:
            if not search_result or not isinstance(search_result, list):
                return f"没有找到「{query}」相关的商品"

            count = len(search_result)
            if count > 0:
                first_item = search_result[0]
                title = first_item.get('itemTitle', first_item.get('title', '商品'))
                price = first_item.get('itemPrice', first_item.get('price', '未知价格'))
                shop_name = first_item.get('seller_name', '')

                summary = f"找到了{count}个相关商品"
                if title:
                    summary += f"，包括{title}"
                if price:
                    summary += f"，价格{price}元"
                if shop_name:
                    summary += f"，来自{shop_name}"
                summary += "等"

                return summary
            else:
                return f"没有找到「{query}」相关的商品"

        except Exception as e:
            print(f"生成语音摘要出错: {e}")
            return f"找到了一些「{query}」相关的商品"

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """发送接收到的音频帧到WebSocket"""
        if not self.ws or self.ws.close_code:
            return
        try:
            _, array = frame
            array = array.squeeze()
            audio_message = base64.b64encode(array.tobytes()).decode("utf-8")
            message = {"type": "input_audio_buffer.append", "audio": audio_message}
            await self.ws.send_str(json.dumps(message))
        except aiohttp.ClientConnectionError as e:
            print("Connection closed while sending:", e)
            return

    async def emit(self) -> tuple[int, np.ndarray] | AdditionalOutputs | None:
        """收集多个音频块并返回"""
        item = await wait_for_item(self.voice_queue)
        if not isinstance(item, tuple):
            return item

        sample_rate, first_chunk = item
        if type(first_chunk) == type(None):
            return

        audio_chunks = [first_chunk]

        min_samples = int(SAMPLE_RATE * 0.1)
        while audio_chunks and audio_chunks[0].shape[1] < min_samples:
            try:
                extra = self.voice_queue.get_nowait()
                if isinstance(extra, tuple):
                    _, chunk = extra
                    audio_chunks.append(chunk)
                else:
                    await self.voice_queue.put(extra)
                    break
            except asyncio.QueueEmpty:
                break

        full_audio = np.concatenate(audio_chunks, axis=1)
        return (sample_rate, full_audio)

    async def shutdown(self) -> None:
        """关闭WebSocket和会话"""
        if self.ws:
            await self.ws.close()
            self.ws = None
        if self.session:
            await self.session.close()
            self.session = None

def is_punctuation_only(transcript):
    """检查是否只包含标点符号"""
    return all(c in string.punctuation for c in transcript) and len(transcript) > 0

def update_chatbot(chatbot: list[dict], response) -> list[dict]:
    """更新聊天机器人消息"""
    if hasattr(response, 'transcript'):
        chatbot.append({"role": "assistant", "content": response.transcript})
    return chatbot

# 创建Gradio组件
chatbot = gr.Chatbot(type="messages")
latest_message = gr.Textbox(type="text", visible=False)

# 创建Stream实例
stream = Stream(
    SearchIntegratedAudioHandler(),
    mode="send-receive",
    modality="audio",
    additional_inputs=[chatbot],
    additional_outputs=[chatbot],
    additional_outputs_handler=update_chatbot,
    rtc_configuration=get_twilio_turn_credentials() if get_space() else None,
    concurrency_limit=5 if get_space() else None,
    time_limit=90 if get_space() else None,
)

# 创建FastAPI应用
app = FastAPI()
stream.mount(app)

# 添加静态文件服务，用于提供缓存的图片
image_cache_dir = Path(__file__).parent / "image_cache"
image_cache_dir.mkdir(exist_ok=True)
app.mount("/image_cache", StaticFiles(directory=str(image_cache_dir)), name="image_cache")

@app.get("/")
async def _():
    """主页面"""
    rtc_config = get_twilio_turn_credentials() if get_space() else None
    print('cur_dir:', cur_dir)

    # 使用realtime_chat目录下的index.html
    html_path = cur_dir / "thirdparty" / "realtime_chat" / "index.html"
    if html_path.exists():
        html_content = html_path.read_text()
        html_content = html_content.replace("__RTC_CONFIGURATION__", json.dumps(rtc_config))
        return HTMLResponse(content=html_content)
    else:
        # 如果找不到HTML文件，返回简单的页面
        simple_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>实时语音商品搜索</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>实时语音商品搜索系统</h1>
            <p>HTML文件未找到，请检查 thirdparty/realtime_chat/index.html 是否存在</p>
            <p>RTC配置: {json.dumps(rtc_config)}</p>
        </body>
        </html>
        """
        return HTMLResponse(content=simple_html)

@app.get("/outputs")
def _(webrtc_id: str):
    """输出流接口"""
    async def output_stream():
        async for output in stream.output_stream(webrtc_id):
            try:
                # 处理不同类型的输出
                if hasattr(output.args[0], 'type'):
                    output_type = output.args[0].type
                    if output_type == 'search_results':
                        # 搜索结果特殊处理
                        s = json.dumps({
                            "role": "assistant",
                            "content": output.args[0].content,
                            "id": output.args[0].id,
                            "type": "search_results",
                            "raw_data": getattr(output.args[0], 'raw_data', None),
                            "batch_info": getattr(output.args[0], 'batch_info', None)
                        })
                    elif output_type == 'product_control':
                        # 商品控制事件特殊处理
                        s = json.dumps({
                            "role": "assistant",
                            "content": output.args[0].content,
                            "id": output.args[0].id,
                            "type": "product_control",
                            "action": getattr(output.args[0], 'action', None),
                            "product_index": getattr(output.args[0], 'product_index', None),
                            "selected_product": getattr(output.args[0], 'selected_product', None)
                        })
                    elif output_type == 'volume_control':
                        # 音量控制事件特殊处理
                        s = json.dumps({
                            "role": "assistant",
                            "content": output.args[0].content,
                            "id": output.args[0].id,
                            "type": "volume_control",
                            "action": getattr(output.args[0], 'action', None),
                            "volume": getattr(output.args[0], 'volume', None)
                        })
                    else:
                        # 普通消息
                        s = json.dumps({
                            "role": "assistant",
                            "content": output.args[0].content,
                            "id": output.args[0].id,
                            "type": output.args[0].type
                        })
                else:
                    # 兼容原有格式
                    s = json.dumps({
                        "role": "assistant",
                        "content": getattr(output.args[0], 'content', str(output.args[0])),
                        "id": getattr(output.args[0], 'id', 'unknown'),
                        "type": getattr(output.args[0], 'type', 'msg')
                    })

                yield f"event: output\ndata: {s}\n\n"

            except Exception as e:
                print(f"输出流处理错误: {e}")
                error_s = json.dumps({
                    "role": "assistant",
                    "content": "消息处理出错",
                    "id": "error",
                    "type": "error"
                })
                yield f"event: output\ndata: {error_s}\n\n"

    return StreamingResponse(output_stream(), media_type="text/event-stream")

if __name__ == "__main__":
    import os

    if (mode := os.getenv("MODE")) == "UI":
        stream.ui.launch(server_port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')
    elif mode == "PHONE":
        stream.fastphone(host="0.0.0.0", port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')
    else:
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=7878, ssl_certfile='./cert.pem', ssl_keyfile='./key.pem')