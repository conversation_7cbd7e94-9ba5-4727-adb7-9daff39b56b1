#!/usr/bin/env python3
"""
测试格式转换修复
验证各种情况下的格式转换是否正常工作
"""

import json
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_convert_compact_to_standard():
    """测试格式转换方法"""
    
    # 模拟转换方法
    def convert_compact_to_standard(compact_result: dict, transcript: str) -> dict:
        """将精简格式转换为标准格式"""
        try:
            # 提取精简字段
            tts_text = compact_result.get('tts', '')
            internal_text = compact_result.get('internal', '')
            call_info = compact_result.get('call', {})
            intent = compact_result.get('intent', 'interaction')
            
            # 处理call字段可能为None的情况
            if call_info is None:
                call_info = {}
            
            # 构建标准格式
            standard_result = {
                "tts_text": tts_text,
                "internal_text": internal_text,
                "function_call": {
                    "function_name": call_info.get('name', 'null') if isinstance(call_info, dict) else 'null',
                    "parameters": {
                        "query": call_info.get('query', transcript) if isinstance(call_info, dict) else transcript,
                        "original_query": transcript,
                        "rewritten_query": call_info.get('query', transcript) if isinstance(call_info, dict) else transcript
                    }
                },
                "intent": intent,
                "confidence": 0.9,  # 默认置信度
                "conversation_update": {
                    "intent": intent,
                    "user_input": transcript,
                    "agent_response": tts_text
                }
            }
            
            print(f"🔄 格式转换成功: 精简 -> 标准")
            return standard_result
            
        except Exception as e:
            print(f"❌ 格式转换失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回默认的交互响应
            return {
                "tts_text": "好的，我明白了。",
                "internal_text": "格式转换失败，使用默认响应",
                "function_call": {"function_name": "null"},
                "intent": "interaction",
                "confidence": 0.5,
                "conversation_update": {
                    "intent": "interaction",
                    "user_input": transcript,
                    "agent_response": "好的，我明白了。"
                }
            }
    
    print("🧪 开始测试格式转换修复")
    print("="*60)
    
    # 测试用例
    test_cases = [
        {
            "name": "问候场景 - call为None",
            "compact": {
                "tts": "你好！有什么可以帮助你的吗？",
                "internal": "用户进行了问候",
                "call": None,
                "intent": "interaction"
            },
            "transcript": "你好"
        },
        {
            "name": "问候场景 - call为正确格式",
            "compact": {
                "tts": "你好！有什么可以帮助你的吗？",
                "internal": "用户进行了问候",
                "call": {"name": "null"},
                "intent": "interaction"
            },
            "transcript": "你好"
        },
        {
            "name": "搜索场景 - 正常格式",
            "compact": {
                "tts": "正在为您查找小米14。",
                "internal": "搜索小米14手机",
                "call": {"name": "search_products", "query": "小米14手机"},
                "intent": "search_execute"
            },
            "transcript": "我要买小米14"
        },
        {
            "name": "搜索场景 - call缺少query",
            "compact": {
                "tts": "正在为您查找路由器。",
                "internal": "搜索路由器",
                "call": {"name": "search_products"},
                "intent": "search_execute"
            },
            "transcript": "我想买路由器"
        },
        {
            "name": "异常情况 - call为字符串",
            "compact": {
                "tts": "好的",
                "internal": "异常情况",
                "call": "invalid",
                "intent": "interaction"
            },
            "transcript": "测试"
        },
        {
            "name": "缺少字段",
            "compact": {
                "tts": "好的"
            },
            "transcript": "测试"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        compact_result = test_case['compact']
        transcript = test_case['transcript']
        
        print(f"📥 输入精简格式:")
        print(json.dumps(compact_result, ensure_ascii=False, indent=2))
        
        try:
            standard_result = convert_compact_to_standard(compact_result, transcript)
            
            print(f"\n📤 输出标准格式:")
            print(json.dumps(standard_result, ensure_ascii=False, indent=2))
            
            # 验证关键字段
            assert 'tts_text' in standard_result
            assert 'internal_text' in standard_result
            assert 'function_call' in standard_result
            assert 'intent' in standard_result
            assert 'function_name' in standard_result['function_call']
            assert 'parameters' in standard_result['function_call']
            
            print(f"✅ 测试通过")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "="*60)
    print("🎉 格式转换测试完成")

def test_llm_response_formats():
    """测试不同的LLM响应格式"""
    print(f"\n🧪 测试LLM可能返回的各种格式")
    print("="*60)
    
    # 模拟LLM可能返回的各种格式
    llm_responses = [
        {
            "name": "标准格式",
            "response": '{"tts": "你好！", "internal": "问候", "call": {"name": "null"}, "intent": "interaction"}'
        },
        {
            "name": "call为null",
            "response": '{"tts": "你好！", "internal": "问候", "call": null, "intent": "interaction"}'
        },
        {
            "name": "call为字符串null",
            "response": '{"tts": "你好！", "internal": "问候", "call": "null", "intent": "interaction"}'
        },
        {
            "name": "缺少call字段",
            "response": '{"tts": "你好！", "internal": "问候", "intent": "interaction"}'
        },
        {
            "name": "搜索格式",
            "response": '{"tts": "正在搜索...", "internal": "搜索", "call": {"name": "search_products", "query": "手机"}, "intent": "search_execute"}'
        },
        {
            "name": "格式错误",
            "response": '{"tts": "你好！", "internal": "问候", "call": {"name": "null"}, "intent": "interaction"'  # 缺少结尾括号
        }
    ]
    
    for i, test in enumerate(llm_responses, 1):
        print(f"\n🧪 测试 {i}: {test['name']}")
        print("-" * 30)
        
        response = test['response']
        print(f"📥 LLM响应: {response}")
        
        try:
            # 尝试解析JSON
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                compact_result = json.loads(json_str)
                print(f"✅ JSON解析成功: {compact_result}")
                
                # 检查call字段类型
                call_info = compact_result.get('call')
                if call_info is None:
                    print(f"⚠️ call字段为None，需要处理")
                elif isinstance(call_info, dict):
                    print(f"✅ call字段为字典格式")
                else:
                    print(f"⚠️ call字段为其他类型: {type(call_info)}")
                    
            else:
                print(f"❌ 未找到有效的JSON格式")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    test_convert_compact_to_standard()
    test_llm_response_formats()
