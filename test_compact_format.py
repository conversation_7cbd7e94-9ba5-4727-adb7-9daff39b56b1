#!/usr/bin/env python3
"""
测试精简格式的效果
对比优化前后的token使用量和生成时间
"""

import asyncio
import json
import time
from unittest.mock import Mock, AsyncMock
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

class MockCompactChat:
    """模拟精简格式的聊天客户端"""
    async def stream_chat_with_interrupt(self, prompt, model=None):
        """模拟流式聊天，返回精简格式响应"""
        # 根据提示内容返回不同的模拟响应
        if "直接返回JSON，无需解释" in prompt:
            # 提取用户输入
            user_input = ""
            if "用户输入:" in prompt:
                start = prompt.find('"') + 1
                end = prompt.find('"', start)
                if start > 0 and end > start:
                    user_input = prompt[start:end]
            
            # 根据用户输入生成精简响应
            if "你好" in user_input:
                response = {
                    "tts": "您好！有什么可以帮您的吗？",
                    "internal": "用户问候",
                    "call": {"name": "null"},
                    "intent": "interaction"
                }
            elif "路由器" in user_input and "想买" in user_input:
                response = {
                    "tts": "您想要买什么品牌的路由器？",
                    "internal": "询问路由器品牌偏好",
                    "call": {"name": "null"},
                    "intent": "search_inquiry"
                }
            elif "小米14" in user_input:
                response = {
                    "tts": "正在为您查找小米14。",
                    "internal": "搜索小米14手机",
                    "call": {"name": "search_products", "query": "小米14手机"},
                    "intent": "search_execute"
                }
            elif "天气" in user_input:
                response = {
                    "tts": "正在查询天气信息...",
                    "internal": "天气查询",
                    "call": {"name": "web_search", "query": "今天天气"},
                    "intent": "web_search"
                }
            else:
                response = {
                    "tts": "抱歉，我没有听清楚。",
                    "internal": "需要澄清",
                    "call": {"name": "null"},
                    "intent": "clarification"
                }
            
            # 模拟流式返回
            json_str = json.dumps(response, ensure_ascii=False)
            yield json_str
        else:
            # 其他情况的默认响应
            yield "这是一个模拟的回复。"

class MockVerboseChat:
    """模拟冗余格式的聊天客户端"""
    async def stream_chat_with_interrupt(self, prompt, model=None):
        """模拟流式聊天，返回冗余格式响应"""
        # 根据提示内容返回不同的模拟响应
        if "三部分响应" in prompt or "JSON格式" in prompt:
            # 提取用户输入
            user_input = ""
            if "用户当前输入" in prompt:
                start = prompt.find('"') + 1
                end = prompt.find('"', start)
                if start > 0 and end > start:
                    user_input = prompt[start:end]
            
            # 根据用户输入生成冗余响应
            if "你好" in user_input:
                response = {
                    "tts_text": "您好！很高兴为您服务，有什么可以帮您的吗？",
                    "internal_text": "用户问候，建立对话连接，准备为用户提供购物服务",
                    "function_call": {
                        "function_name": "null",
                        "parameters": {
                            "query": "",
                            "original_query": user_input,
                            "rewritten_query": ""
                        }
                    },
                    "intent": "interaction",
                    "confidence": 0.95,
                    "conversation_update": {
                        "intent": "interaction",
                        "user_input": user_input,
                        "agent_response": "您好！很高兴为您服务，有什么可以帮您的吗？"
                    }
                }
            elif "小米14" in user_input:
                response = {
                    "tts_text": "正在为您查找小米14的相关信息。",
                    "internal_text": "用户明确要求购买小米14，执行商品搜索",
                    "function_call": {
                        "function_name": "search_products",
                        "parameters": {
                            "query": "小米14",
                            "original_query": user_input,
                            "rewritten_query": "小米14手机"
                        }
                    },
                    "intent": "search_execute",
                    "confidence": 1.0,
                    "conversation_update": {
                        "intent": "search_execute",
                        "user_input": user_input,
                        "agent_response": "正在为您查找小米14的相关信息。"
                    }
                }
            else:
                response = {
                    "tts_text": "抱歉，我没有听清楚，您能再说一遍吗？",
                    "internal_text": "用户输入不明确，需要进一步澄清用户意图",
                    "function_call": {
                        "function_name": "null",
                        "parameters": {
                            "query": "",
                            "original_query": user_input,
                            "rewritten_query": ""
                        }
                    },
                    "intent": "clarification",
                    "confidence": 0.6,
                    "conversation_update": {
                        "intent": "clarification",
                        "user_input": user_input,
                        "agent_response": "抱歉，我没有听清楚，您能再说一遍吗？"
                    }
                }
            
            # 模拟流式返回
            json_str = json.dumps(response, ensure_ascii=False)
            yield json_str
        else:
            # 其他情况的默认响应
            yield "这是一个模拟的回复。"

def count_tokens(text):
    """简单的token计数（按字符数估算）"""
    # 中文字符按2个token计算，英文字符按0.5个token计算
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    return chinese_chars * 2 + other_chars * 0.5

async def test_format_comparison():
    """测试格式对比"""
    print("🧪 开始测试精简格式 vs 冗余格式")
    print("="*80)
    
    # 创建模拟客户端
    compact_chat = MockCompactChat()
    verbose_chat = MockVerboseChat()
    
    test_cases = [
        "你好",
        "我要买小米14",
        "今天天气怎么样",
        "我想买个路由器"
    ]
    
    total_compact_tokens = 0
    total_verbose_tokens = 0
    total_compact_time = 0
    total_verbose_time = 0
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {user_input}")
        print("-" * 60)
        
        # 测试精简格式
        print("\n✨ 精简格式:")
        compact_prompt = f"""用户输入: "{user_input}"
对话历史: 无
搜索上下文: 无

分析用户意图并返回JSON:
- interaction: 问候/闲聊
- search_inquiry: 询问商品偏好  
- search_execute: 明确搜索请求
- clarification: 需要澄清
- no_intent: 无意义内容

原则: 低延迟优先，精准判断，自然对话

JSON格式:
{{
    "tts": "简洁的语音回复",
    "internal": "内部记录信息",
    "call": {{"name": "search_products|web_search|null", "query": "搜索词"}},
    "intent": "意图类型"
}}

直接返回JSON，无需解释："""
        
        start_time = time.time()
        compact_response = ""
        async for chunk in compact_chat.stream_chat_with_interrupt(compact_prompt):
            compact_response += chunk
        compact_time = time.time() - start_time
        
        compact_tokens = count_tokens(compact_response)
        total_compact_tokens += compact_tokens
        total_compact_time += compact_time
        
        print(f"   响应: {compact_response}")
        print(f"   Token数: {compact_tokens:.0f}")
        print(f"   生成时间: {compact_time:.3f}秒")
        
        # 测试冗余格式
        print("\n🔄 冗余格式:")
        verbose_prompt = f"""
        你是一个专业的实时语音购物助手。请对用户输入进行智能分析，并返回三部分响应以实现低延迟交互。

        **用户当前输入**："{user_input}"

        **对话历史摘要**：无

        **搜索上下文**：无

        **重要原则**：
        1. **低延迟优先**：TTS文本要简洁自然，适合实时语音交互
        2. **对话感优先**：保持自然的对话流，避免生硬的系统提示
        3. **精准判断**：用户没有明确搜索意图时，不要调用搜索工具
        4. **智能澄清**：需要更多信息时，用自然的问句引导用户

        **三部分响应格式**：
        {{
            "tts_text": "立即播报给用户的语音文本（简洁自然，适合语音交互）",
            "internal_text": "记录到对话历史但不播报的内容（如改写的查询、内部分析等）",
            "function_call": {{
                "function_name": "search_products|web_search|null",
                "parameters": {{
                    "query": "搜索查询词",
                    "original_query": "用户原始输入",
                    "rewritten_query": "改写后的查询"
                }}
            }},
            "intent": "用户意图类型",
            "confidence": 0.0-1.0,
            "conversation_update": {{
                "intent": "记录到对话历史的意图",
                "user_input": "{user_input}",
                "agent_response": "tts_text的内容"
            }}
        }}

        请基于当前对话上下文，生成合适的三部分响应：
        """
        
        start_time = time.time()
        verbose_response = ""
        async for chunk in verbose_chat.stream_chat_with_interrupt(verbose_prompt):
            verbose_response += chunk
        verbose_time = time.time() - start_time
        
        verbose_tokens = count_tokens(verbose_response)
        total_verbose_tokens += verbose_tokens
        total_verbose_time += verbose_time
        
        print(f"   响应: {verbose_response[:200]}...")
        print(f"   Token数: {verbose_tokens:.0f}")
        print(f"   生成时间: {verbose_time:.3f}秒")
        
        # 对比
        token_reduction = (verbose_tokens - compact_tokens) / verbose_tokens * 100
        time_reduction = (verbose_time - compact_time) / verbose_time * 100
        
        print(f"\n📊 本次对比:")
        print(f"   Token减少: {token_reduction:.1f}%")
        print(f"   时间减少: {time_reduction:.1f}%")
    
    # 总体对比
    print(f"\n" + "="*80)
    print("📈 总体优化效果")
    print("="*80)
    
    avg_compact_tokens = total_compact_tokens / len(test_cases)
    avg_verbose_tokens = total_verbose_tokens / len(test_cases)
    avg_compact_time = total_compact_time / len(test_cases)
    avg_verbose_time = total_verbose_time / len(test_cases)
    
    overall_token_reduction = (avg_verbose_tokens - avg_compact_tokens) / avg_verbose_tokens * 100
    overall_time_reduction = (avg_verbose_time - avg_compact_time) / avg_verbose_time * 100
    
    print(f"📊 平均Token使用:")
    print(f"   冗余格式: {avg_verbose_tokens:.0f} tokens")
    print(f"   精简格式: {avg_compact_tokens:.0f} tokens")
    print(f"   减少幅度: {overall_token_reduction:.1f}%")
    
    print(f"\n⏱️ 平均生成时间:")
    print(f"   冗余格式: {avg_verbose_time:.3f}秒")
    print(f"   精简格式: {avg_compact_time:.3f}秒")
    print(f"   减少幅度: {overall_time_reduction:.1f}%")
    
    print(f"\n🎉 优化成果:")
    print(f"   ✅ Token使用量减少 {overall_token_reduction:.1f}%")
    print(f"   ✅ 生成时间减少 {overall_time_reduction:.1f}%")
    print(f"   ✅ 保持功能完整性")
    print(f"   ✅ 提升响应效率")

if __name__ == "__main__":
    asyncio.run(test_format_comparison())
